@include('departments/header')

<div class="container-scroller">
    <!-- partial:partials/_navbar.html -->
    @include('departments/topbar')
    <!-- partial -->
    <div class="container-fluid page-body-wrapper">

        @include('departments/sidebar')

        <!-- partial -->
        <div class="main-panel">
            <div class="content-wrapper p-2">
                <div class="bg-primary-subtle d-flex justify-content-between align-middle p-2 rounded border">

                    <div>
                        <img src="{{ asset('images/cmsir.jpg') }}" width="60px" height="auto" />
                    </div>
                    <div class="dascen">
                        {{-- <h3><b>Verified BP Numbers - {{ $ulb->name }}</b></h3> --}}
                    </div>
                    <div>
                        <img src="{{ asset('images/newdeptycm.jpeg') }}" width="60px" height="auto" />
                    </div>
                </div>
                <div class="d-flex mt-3 mb-2 justify-content-between align-items-center">
                    <div>
                        <button type="button" onclick="window.history.back()"
                            class="btn btn-dark rounded btn-sm">Back</button>
                    </div>
                    <div>
                        <a href="{{ route('export.excel.bp.all.august') }}" class="btn btn-success btn-sm">
                            <i class="fas fa-file-excel"></i> Export Excel
                        </a>
                    </div>
                </div>
                <div class="table-responsive"></div>
                    <table class="table table-bordered table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>Sl.No</th>
                                <th>BP Number</th>
                                <th>Consumer Name</th>
                                <th>Tariff Type</th>
                                <th>Address</th>
                                <th>Nikay BP August Total Balance</th>
                                <th>Bakaya Rashis Total Balance</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($datas as $key => $data)
                                <tr>
                                    <td>{{ $datas->firstItem() + $key }}</td>
                                    <td>{{ $data->bp_number }}</td>
                                    <td>{{ $data->consumer_name ?? 'N/A' }}</td>
                                    <td>{{ $data->tariff_type ?? 'N/A' }}</td>
                                    <td>{{ $data->address ?? 'N/A' }}</td>
                                    <td>{{ number_format($data->total_balance ?? 0, 2) }}</td>
                                    <td>{{ number_format($data->bakaya_total_balance ?? 0, 2) }}</td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="7" class="text-center">No records found</td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                    <div class="d-flex justify-content-end">
                        {{-- Using Bootstrap 5 pagination view. For Bootstrap 4 use 'pagination::bootstrap-4' --}}
                        {{ $datas->links('pagination::bootstrap-5') }}
                    </div>
                </div>

                <footer class="footer mt-4">
                    <div class="d-sm-flex justify-content-center justify-content-sm-between">
                        <span class="text-muted text-center text-sm-left d-block d-sm-inline-block">Copyright © 2024. <a
                                href="#" target="_blank">Designed by Datacenter</a>. All rights reserved.</span>
                    </div>
                </footer>
            </div>
        </div>
    </div>
</div>

@include('departments/footer')
