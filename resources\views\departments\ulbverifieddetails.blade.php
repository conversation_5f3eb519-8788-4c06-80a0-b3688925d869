@include('departments/header')

<div class="container-scroller">
    <!-- partial:partials/_navbar.html -->
    @include('departments/topbar')
    <!-- partial -->
    <div class="container-fluid page-body-wrapper">

        @include('departments/sidebar')

        <!-- partial -->
        <div class="main-panel">
            <div class="content-wrapper p-2">
                <div class="bg-primary-subtle d-flex justify-content-between align-middle p-2 rounded border">

                    <div>
                        <img src="{{ asset('images/cmsir.jpg') }}" width="60px" height="auto" />
                    </div>
                    <div class="dascen">
                        <h3><b>Verified BP Numbers - {{ $ulb->name }}</b></h3>
                    </div>
                    <div>
                        <img src="{{ asset('images/newdeptycm.jpeg') }}" width="60px" height="auto" />
                    </div>
                </div>
                <div class="d-flex mt-3 mb-2 justify-content-between align-items-center">
                    <div>
                        <button type="button" onclick="window.history.back()"
                            class="btn btn-dark rounded btn-sm">Back</button>
                    </div>
                    <div class="text-center">
                        <h5 class="text-primary mb-0">Total Verified BP Numbers: {{ count($bpDetails) }}</h5>
                        <small class="text-muted">ULB: {{ $ulb->name }}</small>
                    </div>
                    <div>
                        @if (!empty($bpDetails))
                            <button class="btn btn-success btn-sm" onclick="exportToCSV()">
                                <i class="fas fa-download"></i> Export CSV
                            </button>
                        @endif
                    </div>
                </div>

                <!-- Search Box -->
                @if (!empty($bpDetails))
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-search"></i></span>
                            <input type="text" class="form-control" id="searchInput" placeholder="Search BP Number..." onkeyup="searchTable()">
                        </div>
                    </div>
                    {{-- <div class="col-md-6">
                        <select class="form-select" id="statusFilter" onchange="filterByStatus()">
                            <option value="">All Records</option>
                            <option value="with-data">With Bakaya Data</option>
                            <option value="without-data">Without Bakaya Data</option>
                        </select>
                    </div> --}}
                </div>
                @endif

                <div class="mt-3 desktopset">
                    <div class="mb-3">
                        @if (empty($bpDetails))
                            <div class="alert alert-info text-center">
                                <h5>No verified BP numbers found for this ULB.</h5>
                            </div>
                        @else
                            <div class="table-responsive">
                                <table class="table table-bordered table-hover" id="bpDetailsTable">
                                    <thead class="table-dark">
                                        <tr>
                                            <th scope="col" class="text-center">S.No</th>
                                            <th scope="col" class="text-center">BP Number</th>
                                            <th scope="col" class="text-center">Verification Status</th>
                                            <th scope="col" class="text-center">Verified Date</th>
                                            <th scope="col" class="text-center">Total Balance (₹)</th>
                                            {{-- <th scope="col" class="text-center">Bakaya Status</th> --}}
                                        </tr>
                                    </thead>
                                <tbody>
                                    @foreach ($bpDetails as $index => $detail)
                                        <tr class="table-light">
                                            <th scope="row" class="text-center">{{ $index + 1 }}</th>
                                            <td class="text-center">
                                                <strong>{{ $detail['verification']->bp_number }}</strong>
                                            </td>
                                            <td class="text-center">
                                                <span class="badge bg-success">Verified</span>
                                            </td>
                                            <td class="text-center">
                                                {{ $detail['verification']->verified_at ? $detail['verification']->verified_at->format('d-m-Y H:i') : 'N/A' }}
                                            </td>
                                            <td class="text-center">
                                                @if($detail['bakaya_detail'])
                                                    <span class="text-danger fw-bold">
                                                        ₹{{ number_format($detail['bakaya_detail']->total_balance, 2) }}
                                                    </span>
                                                @else
                                                    <span class="text-muted">No Data</span>
                                                @endif
                                            </td>
                                            {{-- <td class="text-center">
                                                @if($detail['bakaya_detail'])
                                                    <span class="badge bg-warning text-dark">Available</span>
                                                @else
                                                    <span class="badge bg-secondary">Not Found</span>
                                                @endif
                                            </td> --}}
                                        </tr>
                                    @endforeach
                                </tbody>
                                {{-- <tfoot class="table-success">
                                    <tr>
                                        <th colspan="4" class="text-end">Grand Total:</th>
                                        <th class="text-center">
                                            ₹{{ number_format(collect($bpDetails)->sum(function($detail) {
                                                return $detail['bakaya_detail'] ? (float)$detail['bakaya_detail']->total_balance : 0;
                                            }), 2) }}
                                        </th>
                                        <th></th>
                                    </tr>
                                </tfoot> --}}
                                </table>
                            </div>
                        @endif
                    </div>
                </div>

                <footer class="footer mt-4">
                    <div class="d-sm-flex justify-content-center justify-content-sm-between">
                        <span class="text-muted text-center text-sm-left d-block d-sm-inline-block">Copyright © 2024. <a
                                href="#" target="_blank">Designed by Datacenter</a>. All rights reserved.</span>
                    </div>
                </footer>
            </div>
        </div>
    </div>
</div>

@include('departments/footer')

<script>
function searchTable() {
    const input = document.getElementById('searchInput');
    const filter = input.value.toUpperCase();
    const table = document.querySelector('#bpDetailsTable tbody');
    const rows = table.getElementsByTagName('tr');

    for (let i = 0; i < rows.length; i++) {
        if (rows[i].classList.contains('table-success')) continue; // Skip total row

        const bpNumberCell = rows[i].getElementsByTagName('td')[1];
        if (bpNumberCell) {
            const txtValue = bpNumberCell.textContent || bpNumberCell.innerText;
            if (txtValue.toUpperCase().indexOf(filter) > -1) {
                rows[i].style.display = '';
            } else {
                rows[i].style.display = 'none';
            }
        }
    }
}

function filterByStatus() {
    const select = document.getElementById('statusFilter');
    const filter = select.value;
    const table = document.querySelector('#bpDetailsTable tbody');
    const rows = table.getElementsByTagName('tr');

    for (let i = 0; i < rows.length; i++) {
        if (rows[i].classList.contains('table-success')) continue; // Skip total row

        const statusCell = rows[i].getElementsByTagName('td')[5];
        if (statusCell) {
            const badge = statusCell.querySelector('.badge');
            if (badge) {
                const hasData = badge.classList.contains('bg-warning');

                if (filter === '') {
                    rows[i].style.display = '';
                } else if (filter === 'with-data' && hasData) {
                    rows[i].style.display = '';
                } else if (filter === 'without-data' && !hasData) {
                    rows[i].style.display = '';
                } else {
                    rows[i].style.display = 'none';
                }
            }
        }
    }
}

function exportToCSV() {
    const table = document.querySelector('#bpDetailsTable');
    const rows = table.querySelectorAll('tr:not(.table-success)'); // Exclude total row
    let csv = [];

    // Add headers
    const headers = [];
    const headerCells = rows[0].querySelectorAll('th');
    headerCells.forEach(cell => {
        headers.push('"' + cell.textContent.trim() + '"');
    });
    csv.push(headers.join(','));

    // Add data rows
    for (let i = 1; i < rows.length; i++) {
        const row = [];
        const cells = rows[i].querySelectorAll('td, th');
        cells.forEach(cell => {
            let text = cell.textContent.trim();
            // Clean up the text
            text = text.replace(/"/g, '""'); // Escape quotes
            row.push('"' + text + '"');
        });
        csv.push(row.join(','));
    }

    // Download CSV
    const csvContent = csv.join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', 'verified_bp_numbers_{{ $ulb->name }}_{{ date("Y-m-d") }}.csv');
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}
</script>
