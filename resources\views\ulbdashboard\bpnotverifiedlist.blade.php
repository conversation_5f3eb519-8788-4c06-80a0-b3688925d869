@include('ulbdashboard/header')

<div class="container-scroller">
    <!-- partial:partials/_navbar.html -->
    @include('ulbdashboard/topnav')
    <!-- partial -->
    <div class="container-fluid page-body-wrapper">

        @include('ulbdashboard/sidenav')

        <!-- partial -->
        <div class="main-panel">
            <div class="content-wrapper p-2">
                <div class="bg-primary-subtle d-flex justify-content-between align-middle p-2 rounded border">
                    <div>
                        <img src="{{ asset('images/cmsir.jpg') }}" width="60px" height="auto" />
                    </div>
                    <div class="dascen">
                        <h3><b>BP Not Verified Records</b></h3>
                    </div>
                    <div>
                        <img src="{{ asset('images/newdeptycm.jpeg') }}" width="60px" height="auto" />
                    </div>
                </div>

                <div class="d-flex mt-3 mb-2 justify-content-between align-items-center">
                    <div>
                        <button type="button" onclick="window.history.back()"
                            class="btn btn-dark rounded btn-sm">Back</button>
                    </div>
                    <div class="text-center">
                        <h5 class="text-primary mb-0">Total Records: {{ $records->count() }}</h5>
                    </div>
                    <div>
                        <a href="{{ route('bp.not.verified.form') }}" class="btn btn-success btn-sm">
                            <i class="fas fa-plus"></i> Add New Record
                        </a>
                        @if($records->count() > 0)
                        <button class="btn btn-info btn-sm ms-2" onclick="exportToCSV()">
                            <i class="fas fa-download"></i> Export CSV
                        </button>
                        @endif
                    </div>
                </div>

                <!-- Search Box -->
                @if($records->count() > 0)
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-search"></i></span>
                            <input type="text" class="form-control" id="searchInput" 
                                   placeholder="Search BP Number or Address..." onkeyup="searchTable()">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <select class="form-select" id="dateFilter" onchange="filterByDate()">
                            <option value="">All Dates</option>
                            <option value="today">Today</option>
                            <option value="week">This Week</option>
                            <option value="month">This Month</option>
                        </select>
                    </div>
                </div>
                @endif

                <div class="mt-3 desktopset">
                    <div class="mb-3">
                        @if($records->isEmpty())
                            <div class="alert alert-info text-center">
                                <h5>No BP Not Verified records found.</h5>
                                <p>Click "Add New Record" to submit your first record.</p>
                            </div>
                        @else
                            <div class="table-responsive">
                                <table class="table table-bordered table-hover" id="recordsTable">
                                    <thead class="table-dark">
                                        <tr>
                                            <th scope="col" class="text-center">S.No</th>
                                            <th scope="col" class="text-center">BP Number</th>
                                            <th scope="col" class="text-center">Address</th>
                                            <th scope="col" class="text-center">Date of Submission</th>
                                            <th scope="col" class="text-center">Submitted On</th>
                                            <th scope="col" class="text-center">Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($records as $index => $record)
                                        <tr class="table-light">
                                            <th scope="row" class="text-center">{{ $index + 1 }}</th>
                                            <td class="text-center">
                                                <strong class="text-primary">{{ $record->bp_number }}</strong>
                                            </td>
                                            <td id="address-{{ $record->id }}">{{ $record->address }}</td>
                                            <td class="text-center">
                                                {{ $record->date_of_submission->format('d-m-Y') }}
                                            </td>
                                            <td class="text-center">
                                                <small>{{ $record->created_at->format('d-m-Y H:i') }}</small>
                                            </td>
                                            <td class="text-center">
                                                <button class="btn btn-primary btn-sm" 
                                                    onclick="openEditModal({{ $record->id }}, '{{ addslashes($record->address) }}', '{{ $record->bp_number }}')">
                                                    <i class="fas fa-edit"></i> Edit
                                                </button>
                                            </td>
                                        </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        @endif
                    </div>
                </div>

                <footer class="footer mt-4">
                    <div class="d-sm-flex justify-content-center justify-content-sm-between">
                        <span class="text-muted text-center text-sm-left d-block d-sm-inline-block">Copyright © 2024. <a
                                href="#" target="_blank">Designed by Datacenter</a>. All rights reserved.</span>
                    </div>
                </footer>
            </div>
        </div>
    </div>
</div>

<!-- Address Update Modal -->
<div class="modal fade" id="editAddressModal" tabindex="-1" aria-labelledby="editAddressModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="editAddressModalLabel">
                    <i class="fas fa-edit"></i> Update Address
                </h5>
                <button type="button" class="btn-close btn-close-white" onclick="closeModal()" aria-label="Close"></button>
            </div>
            <form id="updateAddressForm">
                @csrf
                <div class="modal-body">
                    <input type="hidden" id="record_id" name="record_id">
                    <div class="mb-3">
                        <label for="bp_number_display" class="form-label">BP Number</label>
                        <input type="text" class="form-control" id="bp_number_display" readonly>
                    </div>
                    <div class="mb-3">
                        <label for="address" class="form-label">Address <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="address" name="address" rows="4" required placeholder="Enter full address..."></textarea>
                        <div class="form-text">Please provide the complete and accurate address.</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="closeModal()">
                        <i class="fas fa-times"></i> Cancel
                    </button>
                    <button type="submit" class="btn btn-primary" id="submitBtn">
                        <i class="fas fa-save"></i> Update Address
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@include('ulbdashboard/footer')

<script>
function searchTable() {
    const input = document.getElementById('searchInput');
    const filter = input.value.toUpperCase();
    const table = document.querySelector('#recordsTable tbody');
    const rows = table.getElementsByTagName('tr');

    for (let i = 0; i < rows.length; i++) {
        const bpNumberCell = rows[i].getElementsByTagName('td')[1];
        const addressCell = rows[i].getElementsByTagName('td')[2];
        
        if (bpNumberCell && addressCell) {
            const bpNumber = bpNumberCell.textContent || bpNumberCell.innerText;
            const address = addressCell.textContent || addressCell.innerText;
            
            if (bpNumber.toUpperCase().indexOf(filter) > -1 || 
                address.toUpperCase().indexOf(filter) > -1) {
                rows[i].style.display = '';
            } else {
                rows[i].style.display = 'none';
            }
        }
    }
}

function filterByDate() {
    const select = document.getElementById('dateFilter');
    const filter = select.value;
    const table = document.querySelector('#recordsTable tbody');
    const rows = table.getElementsByTagName('tr');
    
    const today = new Date();
    const startOfWeek = new Date(today.setDate(today.getDate() - today.getDay()));
    const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);

    for (let i = 0; i < rows.length; i++) {
        const dateCell = rows[i].getElementsByTagName('td')[4];
        
        if (dateCell) {
            const dateText = dateCell.textContent.trim();
            const recordDate = new Date(dateText.split(' ')[0].split('-').reverse().join('-'));
            
            if (filter === '') {
                rows[i].style.display = '';
            } else if (filter === 'today') {
                const isToday = recordDate.toDateString() === new Date().toDateString();
                rows[i].style.display = isToday ? '' : 'none';
            } else if (filter === 'week') {
                const isThisWeek = recordDate >= startOfWeek;
                rows[i].style.display = isThisWeek ? '' : 'none';
            } else if (filter === 'month') {
                const isThisMonth = recordDate >= startOfMonth;
                rows[i].style.display = isThisMonth ? '' : 'none';
            }
        }
    }
}

function exportToCSV() {
    const table = document.querySelector('#recordsTable');
    const rows = table.querySelectorAll('tr');
    let csv = [];
    
    // Add headers
    const headers = [];
    const headerCells = rows[0].querySelectorAll('th');
    headerCells.forEach((cell, index) => {
        // Skip the last column (Action column)
        if (index < headerCells.length - 1) {
            headers.push('"' + cell.textContent.trim() + '"');
        }
    });
    csv.push(headers.join(','));
    
    // Add data rows (only visible ones)
    for (let i = 1; i < rows.length; i++) {
        if (rows[i].style.display !== 'none') {
            const row = [];
            const cells = rows[i].querySelectorAll('td, th');
            cells.forEach((cell, index) => {
                // Skip the last column (Action column)
                if (index < cells.length - 1) {
                    let text = cell.textContent.trim();
                    text = text.replace(/"/g, '""'); // Escape quotes
                    row.push('"' + text + '"');
                }
            });
            csv.push(row.join(','));
        }
    }
    
    // Download CSV
    const csvContent = csv.join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', 'bp_not_verified_records_{{ date("Y-m-d") }}.csv');
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

let editModalInstance = null;

function openEditModal(id, address, bpNumber) {
    document.getElementById('record_id').value = id;
    document.getElementById('address').value = address;
    document.getElementById('bp_number_display').value = bpNumber;
    
    const modalElement = document.getElementById('editAddressModal');
    if (typeof bootstrap !== 'undefined' && bootstrap.Modal) {
        editModalInstance = new bootstrap.Modal(modalElement);
        editModalInstance.show();
    } else if (typeof $ !== 'undefined') {
        // Fallback for jQuery Bootstrap
        $('#editAddressModal').modal('show');
    } else {
        // Manual fallback
        modalElement.style.display = 'block';
        modalElement.classList.add('show');
        document.body.classList.add('modal-open');
        
        // Add backdrop
        const backdrop = document.createElement('div');
        backdrop.className = 'modal-backdrop fade show';
        backdrop.id = 'modalBackdrop';
        document.body.appendChild(backdrop);
    }
}

function closeModal() {
    const modalElement = document.getElementById('editAddressModal');
    
    if (editModalInstance) {
        editModalInstance.hide();
        editModalInstance = null;
    } else if (typeof $ !== 'undefined') {
        $('#editAddressModal').modal('hide');
    } else {
        // Manual close
        modalElement.style.display = 'none';
        modalElement.classList.remove('show');
        document.body.classList.remove('modal-open');
        
        // Remove backdrop
        const backdrop = document.getElementById('modalBackdrop');
        if (backdrop) {
            backdrop.remove();
        }
    }
    
    // Reset form
    document.getElementById('updateAddressForm').reset();
}

document.getElementById('updateAddressForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const recordId = document.getElementById('record_id').value;
    const submitBtn = document.getElementById('submitBtn');
    
    // Disable submit button
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Updating...';
    
    fetch(`/bp-not-verified/update-address/${recordId}`, {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('input[name="_token"]').value
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Show success message
            alert('Address updated successfully!');
            
            // Reload the page
            window.location.reload();
        } else {
            alert('Error updating address: ' + (data.message || 'Unknown error'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while updating the address. Please try again.');
    })
    .finally(() => {
        // Re-enable submit button
        submitBtn.disabled = false;
        submitBtn.innerHTML = '<i class="fas fa-save"></i> Update Address';
    });
});
</script>