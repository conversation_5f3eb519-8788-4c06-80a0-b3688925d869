<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BpNotVerified extends Model
{
    use HasFactory;

    protected $table = 'bp_not_verified';

    protected $fillable = [
        'bp_number',
        'ulb_id',
        'address',
        'date_of_submission'
    ];

    protected $casts = [
        'date_of_submission' => 'date',
    ];

    // Relationship with User (ULB)
    public function ulb()
    {
        return $this->belongsTo(User::class, 'ulb_id');
    }
}
