<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('nikay_bp_augusts', function (Blueprint $table) {
            $table->id();
            $table->string('bp_number')->unique();
            $table->string('consumer_name');
            $table->string('tariff_type');
            $table->string('address');
            $table->decimal('total_balance');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('nikay_bp_augusts');
    }
};
