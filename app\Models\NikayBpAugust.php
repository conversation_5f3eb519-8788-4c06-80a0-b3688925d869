<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class NikayBpAugust extends Model
{
    use HasFactory;

    protected $table = 'nikay_bp_augusts';

    protected $fillable = [
        'bp_number',
        'consumer_name',
        'tariff_type',
        'address',
        'total_balance'
    ];

    protected $casts = [
        'total_balance' => 'decimal:2',
    ];
}
