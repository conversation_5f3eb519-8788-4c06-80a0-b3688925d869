<?php

namespace App\Http\Controllers;


use App\Models\AnyaKarya;
use App\Models\AppatnidhiMarammat;
use App\Models\Bar;

use App\Models\ArrearsReport;
use App\Models\BakayaRashi;
use App\Models\BakayaRashiTwo;

use App\Models\ChungiMadhVyay;
use App\Models\ElectricityBill;
use App\Models\Employee;

use App\Models\JalPradayGirh;
use App\Models\LambitVetan;
use App\Models\LeaveApply;
use App\Models\MahapaurAdhyakshNidhi;
use App\Models\MasterDivision;
use App\Models\MissionCleanCity;
use App\Models\MudrankSulak;

use App\Models\NewMeterRegistration;
use App\Models\ParshadNidhi;
use App\Models\PeyjalkastNivaran;
use App\Models\User;
use App\Models\RevenueCollection;
use App\Models\SalaryDetail;
use App\Models\SalaryDetailPlacement;
use App\Models\SwachCommando;
use App\Models\TaxType;
use App\Models\Utpadkar;
use App\Models\YatriKar;
use Illuminate\Http\Request;

use RealRashid\SweetAlert\Facades\Alert;
use Illuminate\Support\Facades\Auth;
use Maatwebsite\Excel\Facades\Excel;
use App\Imports\DataImport;
use App\Imports\DataimportAppatnidhi;
use App\Imports\DataimportMahapaur;
use App\Imports\DataimportParshadnidhi;
use App\Imports\DataimportSwachcommando;
use App\Imports\DataimportMissioncleancity;
use App\Imports\DataimportLambitVetan;
use App\Imports\DataimportAnyaKarya;
use App\Imports\DataimportPeyjalkast;
use App\Imports\DataimportJalpradgirh;
use App\Imports\DataimportMudrankSulk;
use App\Imports\DataimportBar;
use App\Imports\Dataimportyatrikar;
use App\Imports\DataimportUtpadkar;
use App\Models\Admin;
use App\Models\BpNotVerified;
use App\Models\BpVerification;
use App\Models\Complexinformation;
use App\Models\ElectricityYearlyData;
use App\Models\EmployeePost;
use App\Models\EmployeeRegistration;
use App\Models\EmployeeType;
use App\Models\GardenInfo;
use App\Models\GouthanGovdhamKanjiHouse;
use App\Models\GovtBuilding;
use App\Models\Gym;
use App\Models\HandpumInfo;
use App\Models\IncomeExpenditure;
use App\Models\jdempregistration;
use App\Models\MasterDistrict;
use App\Models\Muktidham;
use App\Models\PanitankiInfo;
use App\Models\Payscale;
use App\Models\PeyjalInfo;
use App\Models\PlaygroundInfo;
use App\Models\RevenueDemand;
use App\Models\StreetlightInfo;
use App\Models\TalabInformation;
use App\Models\Jaljanitbimari;
use App\Models\NikayBpAugust;
use App\Models\RainWaterHarvesting;
use App\Models\SensReport;
use App\Models\SlrmCenterBhawan;
use App\Models\Stp;
use App\Models\Toilet;
use App\Models\TrafficLight;
use App\Models\Wtp;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\IOFactory;


use Illuminate\Support\Facades\Cache;

use Maatwebsite\Excel\Excel as ExcelFormat;
use Illuminate\Support\Collection;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Carbon\Carbon;


class DepatmentController extends Controller
{
    public function departmentdashboard()
    {
        $employeeCount = Employee::count();
        $jdemployeeCount = jdempregistration::count();
        $raipurjdemp = jdempregistration::where('current_jd', '3')->count();
        $raipurulbemp = Employee::whereHas('ulb', function ($query) {
            $query->where('division_id', '3');
        })->count();
        $durgjdemp = jdempregistration::where('current_jd', '2')->count();
        $durgulbemp = Employee::whereHas('ulb', function ($query) {
            $query->where('division_id', '2');
        })->count();
        $bilaspurjdemp = jdempregistration::where('current_jd', '4')->count();
        $bilaspurulbemp = Employee::whereHas('ulb', function ($query) {
            $query->where('division_id', '4');
        })->count();
        $sargujajdemp = jdempregistration::where('current_jd', '5')->count();
        $sargujaulbemp = Employee::whereHas('ulb', function ($query) {
            $query->where('division_id', '5');
        })->count();
        $bastarjdemp = jdempregistration::where('current_jd', '1')->count();
        $bastarulbemp = Employee::whereHas('ulb', function ($query) {
            $query->where('division_id', '1');
        })->count();
        $leaveCount = LeaveApply::count();
        $salaryCount = SalaryDetail::count();
        $meterCount = NewMeterRegistration::count();
        $employeeCounts = Employee::with(['ulb']) // Load relationships
            ->get()
            ->groupBy('current_ulb')
            ->map(function ($group) {
                return [
                    'ulb' => $group->first()->ulb, // Get ULB details
                    'total_registered' => $group->count() // Count employees in each ULB
                ];
            });
        $ulbIdsWithEmployees = Employee::pluck('current_ulb')->toArray();

        $leftoverULBs = User::whereNotIn('id', $ulbIdsWithEmployees)
            ->select('id', 'name', 'ulb_type')
            ->get();

        return view('departments/departmentdashboard', compact(
            'employeeCount',
            'jdemployeeCount',
            'raipurjdemp',
            'raipurulbemp',
            'durgulbemp',
            'durgjdemp',
            'bilaspurjdemp',
            'bilaspurulbemp',
            'sargujajdemp',
            'sargujaulbemp',
            'bastarjdemp',
            'bastarulbemp',
            'leaveCount',
            'salaryCount',
            'meterCount',
            'employeeCounts',
            'leftoverULBs'
        ));
    }

    public function updateStatus($status)
    {
        // Validate the provided status so only accepted values are used.
        if (!in_array($status, ['active', 'inactive'])) {
            abort(404);
        }

        // Update all rows to the new status.
        // Use caution: This updates every record in the "items" table.
        User::query()->update(['status' => $status]);

        // Redirect back with a success message.
        Alert::success('Success!', 'Status updated to ' . $status);
        return redirect()->back();
    }

    // public function deptrajasvasuli(Request $request)
    // {
    //     $taxTypes = TaxType::all();
    //     $query = RevenueCollection::with('taxType')
    //         ->latest('created_at');
    //     if ($request->has('month') && $request->month != '') {
    //         $query->where('month', $request->month); // Filter by month
    //     }
    //     if ($request->has('taxtype') && $request->taxtype != '') {
    //         $query->where('taxtype_id', $request->taxtype); // Filter by tax type
    //     }
    //     if ($request->has('month') && $request->month != '') {
    //         $month = $request->month;
    //         $query->where('month', $month); // Filter by month
    //     }
    //     if ($request->has('from_date') && $request->has('to_date')) {
    //         $fromDate = $request->from_date;
    //         $toDate = $request->to_date;

    //         if (!empty($fromDate) && !empty($toDate)) {
    //             $query->whereBetween('updated_at', [$fromDate . ' 00:00:00', $toDate . ' 23:59:59']);
    //         }
    //     }

    //     // Fetch the revenue collections
    //     $revenueCollections = $query->get()
    //         ->groupBy(function ($item) {

    //             return $item->taxType->id . '-' . $item->month;
    //         })
    //         ->map(function ($collectionGroup) {
    //             return $collectionGroup->first();
    //         });
    //     return view('departments.deptrajasvasuli', compact('revenueCollections', 'taxTypes'));
    // }
    public function deptrajasvasuli(Request $request)
    {
        $taxTypes = TaxType::all();
        $query = RevenueCollection::with('taxType')->latest('created_at');

        // Filter by month
        if ($request->has('month') && $request->month != '') {
            $query->where('month', $request->month);
        }

        // Filter by tax type
        if ($request->has('taxtype') && $request->taxtype != '') {
            $query->where('taxtype_id', $request->taxtype);
        }

        // Filter by from and to dates
        if ($request->has('from_date') && $request->has('to_date')) {
            $fromDate = $request->from_date;
            $toDate = $request->to_date;

            if (!empty($fromDate) && !empty($toDate)) {
                $query->whereBetween('updated_at', [$fromDate . ' 00:00:00', $toDate . ' 23:59:59']);
            }
        }

        // ✅ Apply percentage filter using `whereRaw()`
        if ($request->has('percentage_filter') && $request->percentage_filter != '') {
            $percentageFilter = $request->percentage_filter;

            if ($percentageFilter == 'greater_than_50') {
                $query->whereRaw('(current_demand + pending_demand) > 0 AND ((current_collection + pending_collection) / (current_demand + pending_demand)) * 100 > 50');
            } elseif ($percentageFilter == 'less_than_50') {
                $query->whereRaw('(current_demand + pending_demand) > 0 AND ((current_collection + pending_collection) / (current_demand + pending_demand)) * 100 <= 50');
            } elseif ($percentageFilter == 'greater_than_30') {
                $query->whereRaw('(current_demand + pending_demand) > 0 AND ((current_collection + pending_collection) / (current_demand + pending_demand)) * 100 > 30');
            }
        }

        // Fetch the revenue collections and group by taxType and month
        $revenueCollections = $query->get()
            ->groupBy(function ($item) {
                return $item->taxType->id . '-' . $item->month;
            })
            ->map(function ($collectionGroup) {
                return $collectionGroup->first();
            });

        return view('departments.deptrajasvasuli', compact('revenueCollections', 'taxTypes'));
    }

    public function deptrajasvasulireport()
    {
        $allDemands = RevenueCollection::get();
        $latestDemandsByUlb = $allDemands->groupBy('ulb_id');

        $ulbIds = $latestDemandsByUlb->keys();
        $ulbs = User::with('district')->whereIn('id', $ulbIds)->get();

        return view('departments.deptrajasvasulireport', compact('ulbs', 'latestDemandsByUlb'));
    }

    public function deptrajasvasuliedited($ulbId)
    {

        $revenueCollections = RevenueCollection::with('taxType')
            ->where('ulb_id', $ulbId)
            ->get();
        $allDemands = RevenueDemand::where('ulb_id', $ulbId)->get();
        $latestDemands = $allDemands->groupBy('taxtype_id')->map(function ($group) {
            return $group->sortByDesc('financial_year')->first();
        });
        $demandByTaxType = $latestDemands;
        $taxTypes = TaxType::all();

        return view('departments.deptrajasvasuliedited', compact(
            'revenueCollections',
            'taxTypes',
            'demandByTaxType'
        ));
    }

    public function deptrevenueupdate(Request $request, $id)
    {
        $collection = RevenueCollection::findOrFail($id);
        $collection->update($request->only([
            'unique_no',
            'month',
            'tax_type_id',
            'current_demand',
            'current_collection',
            'pending_demand',
            'pending_collection',
        ]));
        Alert::success('Success!', 'Record updated successfully.');
        return redirect()->back();
    }

    public function ulbwiserajasvasulli(Request $request)
    {

        // Initialize the query for RevenueCollection
        $item = RevenueCollection::query();

        // Check if there's a filter for ULB Name (Nikay)
        if ($request->has('ulb_name') && $request->ulb_name != '') {
            $searchItem = $request->ulb_name;
            $item->whereHas('ulb', function ($query) use ($searchItem) {
                $query->where('name', 'like', '%' . $searchItem . '%');
            });
        }

        // Check if there's a filter for the Month
        if ($request->has('month') && $request->month != '') {
            $month = $request->month;
            $item->where('month', $month); // Filter by month
        }
        if ($request->has('from_date') && $request->has('to_date')) {
            $fromDate = $request->from_date;
            $toDate = $request->to_date;

            if (!empty($fromDate) && !empty($toDate)) {
                $item->whereBetween('updated_at', [$fromDate . ' 00:00:00', $toDate . ' 23:59:59']);
            }
        }


        // Group and select required columns
        $items = $item->selectRaw('
            ulb_id,
            month,
            SUM(current_demand) as total_current_demand,
            SUM(current_collection) as total_current_collection,
            SUM(pending_demand) as total_pending_demand,
            SUM(pending_collection) as total_pending_collection
           
        ')
            ->groupBy('ulb_id', 'month')  // Group by ULB and month
            ->orderBy('ulb_id', 'asc')     // Order by ulb_id for better display
            ->get();

        if ($request->has('percentage_filter') && $request->percentage_filter != '') {
            $percentageFilter = $request->percentage_filter;

            // Filter data based on percentage conditions
            $items = $items->filter(function ($data) use ($percentageFilter) {
                $totalDemand = $data->total_current_demand + $data->total_pending_demand;
                $totalCollection = $data->total_current_collection + $data->total_pending_collection;

                // Calculate percentage
                $percentage = $totalDemand != 0 ? ($totalCollection / $totalDemand) * 100 : 0;

                // Apply the filter conditions based on the selected percentage filter
                if ($percentageFilter == 'greater_than_50' && $percentage >= 50) {
                    return true;
                } elseif ($percentageFilter == 'less_than_50' && $percentage < 50) {
                    return true;
                } elseif ($percentageFilter == 'greater_than_30' && $percentage > 30) {
                    return true;
                }

                return false;  // Do not include if no condition matches
            });
        }

        $nikays = User::all();
        return view('departments.ulbwiserajasvasulli', compact('item', 'nikays', 'items'));
    }

    public function monthwiserajasvasulli(Request $request)
    {
        $taxTypes = TaxType::all();

        $items = RevenueCollection::selectRaw('
            taxtype_id,
            month,
            SUM(current_demand) as total_current_demand,
            SUM(current_collection) as total_current_collection,
            SUM(pending_demand) as total_pending_demand,
            SUM(pending_collection) as total_pending_collection
        ')
            ->groupBy('taxtype_id', 'month')
            ->orderBy('month', 'asc')
            ->orderBy('taxtype_id', 'asc')
            ->get();

        $nikays = User::all();

        return view('departments.monthwiserajasvasulli', compact('taxTypes', 'nikays', 'items'));
    }


    public function divisionwiserajasvasulli(Request $request)
    {
        $lastMonth = \Carbon\Carbon::now()->subMonth()->format('Y-m');  // Get the last month in 'YYYY-MM' format

        //  for raipur (division_id = 3)
        $itemForDivision3 = RevenueCollection::query();
        $itemForDivision3 = $itemForDivision3->join('users', 'users.id', '=', 'revenue_collections.ulb_id')
            ->where('users.division_id', 3)
            ->selectRaw('
                        month,
                        SUM(revenue_collections.current_demand) as total_current_demand,
                        SUM(revenue_collections.current_collection) as total_current_collection,
                        SUM(revenue_collections.pending_demand) as total_pending_demand,
                        SUM(revenue_collections.pending_collection) as total_pending_collection
                    ')
            ->groupBy('month')  // Group by the month column
            ->orderBy('month', 'asc');  // Order by month

        // Apply month filter if provided
        if ($request->has('month') && $request->month != '') {
            $month = $request->month;
            $itemForDivision3->where('revenue_collections.month', $month);  // Filtering by month column
        }

        // Get the results for Division 3 (Raipur)
        $itemForDivision3 = $itemForDivision3->get();

        //  for durg (division_id = 2)
        $itemForDivision2 = RevenueCollection::query();
        $itemForDivision2 = $itemForDivision2->join('users', 'users.id', '=', 'revenue_collections.ulb_id')
            ->where('users.division_id', 2)
            ->selectRaw('
                      month,
                      SUM(revenue_collections.current_demand) as total_current_demand,
                      SUM(revenue_collections.current_collection) as total_current_collection,
                      SUM(revenue_collections.pending_demand) as total_pending_demand,
                      SUM(revenue_collections.pending_collection) as total_pending_collection
                    ')
            ->groupBy('month')  // Group by the month column
            ->orderBy('month', 'asc');  // Order by month

        // Apply month filter if provided
        if ($request->has('month') && $request->month != '') {
            $month = $request->month;
            $itemForDivision2->where('revenue_collections.month', $month);  // Filtering by month column
        }

        // Get the results for Division 2 (Durg)
        $itemForDivision2 = $itemForDivision2->get();

        //  for bilaspur (division_id = 4)
        $itemForDivision4 = RevenueCollection::query();
        $itemForDivision4 = $itemForDivision4->join('users', 'users.id', '=', 'revenue_collections.ulb_id')
            ->where('users.division_id', 4)
            ->selectRaw('
                      month,
                      SUM(revenue_collections.current_demand) as total_current_demand,
                      SUM(revenue_collections.current_collection) as total_current_collection,
                      SUM(revenue_collections.pending_demand) as total_pending_demand,
                      SUM(revenue_collections.pending_collection) as total_pending_collection
                     ')
            ->groupBy('month')  // Group by the month column
            ->orderBy('month', 'asc');  // Order by month

        // Apply month filter if provided
        if ($request->has('month') && $request->month != '') {
            $month = $request->month;
            $itemForDivision4->where('revenue_collections.month', $month);  // Filtering by month column
        }

        // Get the results for Division 4 (Bilaspur)
        $itemForDivision4 = $itemForDivision4->get();

        //  for bastar (division_id = 1)
        $itemForDivision1 = RevenueCollection::query();
        $itemForDivision1 = $itemForDivision1->join('users', 'users.id', '=', 'revenue_collections.ulb_id')
            ->where('users.division_id', 1)
            ->selectRaw('
                      month,
                      SUM(revenue_collections.current_demand) as total_current_demand,
                      SUM(revenue_collections.current_collection) as total_current_collection,
                      SUM(revenue_collections.pending_demand) as total_pending_demand,
                      SUM(revenue_collections.pending_collection) as total_pending_collection
                      ')
            ->groupBy('month')  // Group by the month column
            ->orderBy('month', 'asc');  // Order by month

        // Apply month filter if provided
        if ($request->has('month') && $request->month != '') {
            $month = $request->month;
            $itemForDivision1->where('revenue_collections.month', $month);  // Filtering by month column
        }

        // Get the results for Division 1 (Bastar)
        $itemForDivision1 = $itemForDivision1->get();

        //  for sarguja (division_id = 5)
        $itemForDivision5 = RevenueCollection::query();
        $itemForDivision5 = $itemForDivision5->join('users', 'users.id', '=', 'revenue_collections.ulb_id')
            ->where('users.division_id', 5)
            ->selectRaw('
                      month,
                      SUM(revenue_collections.current_demand) as total_current_demand,
                      SUM(revenue_collections.current_collection) as total_current_collection,
                      SUM(revenue_collections.pending_demand) as total_pending_demand,
                      SUM(revenue_collections.pending_collection) as total_pending_collection
                      ')
            ->groupBy('month')  // Group by the month column
            ->orderBy('month', 'asc');  // Order by month

        // Apply month filter if provided
        if ($request->has('month') && $request->month != '') {
            $month = $request->month;
            $itemForDivision5->where('revenue_collections.month', $month);  // Filtering by month column
        }

        // Get the results for Division 5 (Sarguja)
        $itemForDivision5 = $itemForDivision5->get();

        // Return the view with all division data
        return view('departments.divisionwiserajasvasulli', compact(
            'itemForDivision3',
            'itemForDivision2',
            'itemForDivision4',
            'itemForDivision1',
            'itemForDivision5'
        ));
    }
    public function rajasvasulliform()
    {
        $nikay = Auth::guard('web')->user();
        $ulbs = User::all();
        $taxtypes = TaxType::all();
        $items = RevenueDemand::all();
        return view('departments.deptrajasvasulliform', compact('ulbs', 'nikay', 'taxtypes', 'items'));
    }
    public function rajasvasulliformsubmit(Request $request)
    {
        $request->validate(
            [
                'ulb_id' => 'required',
                'taxtype_id' => 'required',
                'current_demand' => 'required',
                'pending_demand' => 'required',
                'date' => 'required',

            ],
        );
        $currentYear = Carbon::now()->year;
        $financialYear = (Carbon::now()->month >= 4)
            ? $currentYear . '-' . ($currentYear + 1)
            : ($currentYear - 1) . '-' . $currentYear;

        $data = new RevenueDemand();
        $data->ulb_id = $request->input('ulb_id');
        $data->taxtype_id = $request->input('taxtype_id');
        $data->financial_year = $financialYear;
        $data->current_demand = $request->input('current_demand');
        $data->pending_demand = $request->input('pending_demand');
        $data->date = $request->input('date');
        $data->save();
        Alert::success('Success!', 'Data submitted successfully');
        return back();
    }


    // public function deptemployeedetail(Request $request)
    // {
    //     $query = Employee::query();
    //     $searchApplied = false;

    //     if ($request->has('registration_no') && $request->registration_no != '') {
    //         $registrationNumber = $request->registration_no;
    //         $query->where('registration_no', 'like', '%' . $registrationNumber . '%');
    //         $searchApplied = true;
    //     }
    //     if ($request->has('search') && $request->search != '') {
    //         $seachitem = $request->search;
    //         $query->orWhereHas('ulb', function ($q) use ($seachitem) {

    //             $q->where('name', 'like', '%' . $seachitem . '%');
    //         });

    //         // Flag to remove pagination ONLY when ULB search is used
    //         $searchApplied = true;
    //     }
    //     if ($request->has('from_date') && $request->has('to_date')) {
    //         $fromDate = $request->from_date;
    //         $toDate = $request->to_date;

    //         if (!empty($fromDate) && !empty($toDate)) {
    //             $query->whereBetween('updated_at', [$fromDate . ' 00:00:00', $toDate . ' 23:59:59']);
    //             $searchApplied = true;
    //         }
    //     }
    //     if ($request->has('employee_type') && $request->employee_type != '') {
    //         $query->where('employee_type', $request->employee_type);
    //         $searchApplied = true;
    //     }
    //     if ($request->has('post_name') && $request->post_name != '') {
    //         $query->where('post_name', $request->post_name);
    //         $searchApplied = true;
    //     }

    //     // Remove pagination ONLY for ULB search
    //     $items = $searchApplied ? $query->orderBy('id')->get() : $query->orderBy('id')->paginate(100);

    //     $nikays = User::all();
    //     $employeeTypes = EmployeeType::all();
    //     $employeeposts = EmployeePost::all();
    //     $payscale = Payscale::all();

    //     return view('departments/deptemployeedetail', compact('items', 'nikays', 'employeeTypes', 'employeeposts', 'payscale', 'searchApplied'));
    // }

    public function deptemployeedetail(Request $request)
    {

        $query = Employee::query();

        if ($request->has('registration_no') && $request->registration_no != '') {
            $registrationNumber = $request->registration_no;
            $query->where('registration_no', 'like', '%' . $registrationNumber . '%');
        }
        if ($request->has('search') && $request->search != '') {
            $seachitem = $request->search;

            $query->orWhereHas('ulb', function ($q) use ($seachitem) {
                $q->where('name', 'like', '%' . $seachitem . '%');
            });
        }
        if ($request->has('from_date') && $request->has('to_date')) {
            $fromDate = $request->from_date;
            $toDate = $request->to_date;

            if (!empty($fromDate) && !empty($toDate)) {
                $query->whereBetween('updated_at', [$fromDate . ' 00:00:00', $toDate . ' 23:59:59']);
            }
        }
        if ($request->has('employee_type') && $request->employee_type != '') {
            $query->where('employee_type', $request->employee_type);
        }
        if ($request->has('post_name') && $request->post_name != '') {
            $query->where('post_name', $request->post_name);
        }
        // Get the filtered leave applications
        $items = $query->orderBy('id')->paginate(100);
        $nikays = User::all();
        $employeeTypes = EmployeeType::all();
        $employeeposts = EmployeePost::all();
        $payscale = Payscale::all();
        return view('departments/deptemployeedetail', compact('items', 'nikays', 'employeeTypes', 'employeeposts', 'payscale'));
    }

    public function addulbemployee(Request $request)
    {
        // dd($request);
        $validatedData = $request->validate([
            'employee_photo' => 'required|image|mimes:jpeg,jpg,png|max:50',
            'current_ulb' => 'required|integer',
            'post_name' => 'required|integer',
            // 'post_number' => 'nullable|integer',
            'payscale' => 'required|integer',
            'employee_name' => 'required|string|max:255',
            'father_name' => 'required|string|max:255',
            'employee_type' => 'required|integer',
            'birth_date' => 'required|date',
            'gender' => 'required|in:male,female,other',
            'caste' => 'required|string|max:255',
            'qualification' => 'required|string',
            'work_start_date' => 'required|date',
            'current_designation' => 'required|string',
            // 'designation_date' => 'required|date',
            'joining_date' => 'required|date',
            // 'suspension_status' => 'required|string|max:255',
            'original_ulb_name' => 'required|integer',
            'department_name' => 'required|string',
            // 'vacant_post_count' => 'nullable|integer',
            // 'phone_number' => 'nullable|string|max:15',
            'mobile_number' => 'required|string|max:10',
            'email' => 'required|email',
            'temporary_address' => 'required|string',
            'permanent_address' => 'required|string',
            'employee_cl' => 'nullable',
            'employee_ol' => 'nullable',
            'employee_el' => 'nullable',
            'employee_ml' => 'nullable',
            'e_hrms_email' => 'required|string',
            'remarks' => 'nullable|string',
            'reference_id' => [
                'nullable',
                'string',
                'size:12',
                'unique:employees,reference_id',
            ]
        ]);
        if ($request->hasFile('employee_photo')) {
            $file = $request->file('employee_photo');
            $filename = time() . '_' . $file->getClientOriginalName();
            $filePath = $file->storeAs('uploads/employee_photos', $filename, 'public'); // Store file in 'storage/app/public/uploads/employee_photos'
            $validatedData['employee_photo'] = $filePath;
        }
        // $nikay = Auth::guard('web')->user();
        // $ulbidcode = $nikay->ulbidcode;
        $ulb = User::where('id', $request->current_ulb)->first();
        if (!$ulb) {
            return redirect()->back()->withErrors(['current_ulb' => 'Invalid ULB ID']);
        }
        $ulbidcode = $ulb->ulbidcode;
        do {
            $randomNumber = rand(10, 9999);
            $registrationNo = 'Admin' . $ulbidcode . $randomNumber;
        } while (Employee::where('current_ulb', $ulb->id)->where('registration_no', $registrationNo)->exists());

        $validatedData['registration_no'] = $registrationNo;
        $employee = Employee::create($validatedData);
        // dd($employee);

        Alert::success('Success!', 'Employee data submitted successfully!');

        return redirect()->route('dept.employeedetail')->with('success', 'Employee data Submitted successfully!');
    }
    public function deptjdemployeedetail(Request $request)
    {

        $query = jdempregistration::query();

        if ($request->has('registration_no') && $request->registration_no != '') {
            $registrationNumber = $request->registration_no;
            $query->where('registration_no', 'like', '%' . $registrationNumber . '%');
        }
        if ($request->has('search') && $request->search != '') {
            $seachitem = $request->search;

            $query->orWhereHas('jdname', function ($q) use ($seachitem) {
                $q->where('name', 'like', '%' . $seachitem . '%');
            });
        }
        if ($request->has('from_date') && $request->has('to_date')) {
            $fromDate = $request->from_date;
            $toDate = $request->to_date;

            if (!empty($fromDate) && !empty($toDate)) {
                $query->whereBetween('updated_at', [$fromDate . ' 00:00:00', $toDate . ' 23:59:59']);
            }
        }
        if ($request->has('employee_type') && $request->employee_type != '') {
            $query->where('employee_type', $request->employee_type);
        }
        if ($request->has('post_name') && $request->post_name != '') {
            $query->where('post_name', $request->post_name);
        }
        // Get the filtered leave applications
        $items = $query->orderBy('id')->paginate(100);
        $jds = Admin::all();
        $employeeTypes = EmployeeType::all();
        $employeeposts = EmployeePost::all();
        $payscale = Payscale::all();
        $nikays = User::all();
        return view('departments/jdempoyeedetail', compact('items', 'jds', 'nikays', 'employeeTypes', 'employeeposts', 'payscale'));
    }
    public function addjdemployee(Request $request)
    {
        // dd($request);
        $validatedData = $request->validate([
            'employee_photo' => 'required|image|mimes:jpeg,jpg,png|max:50',
            'current_jd' => 'required|integer',
            'post_name' => 'required|integer',
            // 'post_number' => 'nullable|integer',
            'payscale' => 'required|integer',
            'employee_name' => 'required|string|max:255',
            'father_name' => 'required|string|max:255',
            'employee_type' => 'required|integer',
            'birth_date' => 'required|date',
            'gender' => 'required|in:male,female,other',
            'caste' => 'required|string|max:255',
            'qualification' => 'required|string',
            'work_start_date' => 'required|date',
            'current_designation' => 'required|string',
            // 'designation_date' => 'required|date',
            'joining_date' => 'required|date',
            // 'suspension_status' => 'required|string|max:255',
            'original_ulb_name' => 'required|integer',
            'department_name' => 'required|string',
            // 'vacant_post_count' => 'nullable|integer',
            // 'phone_number' => 'nullable|string|max:15',
            'mobile_number' => 'required|string|max:10',
            'email' => 'required|email',
            'temporary_address' => 'required|string',
            'permanent_address' => 'required|string',
            'employee_cl' => 'nullable',
            'employee_ol' => 'nullable',
            'employee_el' => 'nullable',
            'employee_ml' => 'nullable',
            'e_hrms_email' => 'required|string',
            'remarks' => 'nullable|string',
            'reference_id' => [
                'nullable',
                'string',
                'size:12',
                'unique:employees,reference_id',
            ]
        ]);
        if ($request->hasFile('employee_photo')) {
            $file = $request->file('employee_photo');
            $filename = time() . '_' . $file->getClientOriginalName();
            $filePath = $file->storeAs('uploads/employee_photos', $filename, 'public'); // Store file in 'storage/app/public/uploads/employee_photos'
            $validatedData['employee_photo'] = $filePath;
        }
        $ulb = Admin::where('id', $request->current_jd)->first();
        if (!$ulb) {
            return redirect()->back()->withErrors(['current_jd' => 'Invalid ULB ID']);
        }
        $jdcode = $ulb->division_id;

        do {
            $randomNumber = rand(10, 9999);
            $registrationNo = 'Admin0' . $jdcode . $randomNumber;
        } while (jdempregistration::where('current_jd', $ulb->id)->where('registration_no', $registrationNo)->exists());

        $validatedData['registration_no'] = $registrationNo;
        $employee = jdempregistration::create($validatedData);
        // dd($employee);

        Alert::success('Success!', 'Employee data submitted successfully!');

        return redirect()->route('jd.employeedetail')->with('success', 'Employee data Submitted successfully!');
    }

    public function deptleavedetail(Request $request)
    {
        // Initialize the query to fetch leave applications
        $query = LeaveApply::query();

        // Search by Leave Number
        if ($request->has('leave_number') && $request->leave_number != '') {
            $leaveNumber = $request->leave_number;
            $query->where('leave_number', 'like', '%' . $leaveNumber . '%');
        }

        // Search by Nikay Name
        if ($request->has('nikay_name') && $request->nikay_name != '') {
            $nikayName = $request->nikay_name;
            $query->whereHas('ulb', function ($q) use ($nikayName) {
                $q->where('name', 'like', '%' . $nikayName . '%');
            });
        }
        if ($request->has('leave_date_from') && $request->leave_date_from != '') {
            $query->whereDate('leave_date_from', '>=', $request->leave_date_from);
        }

        // Filter by Leave Date To
        if ($request->has('leave_date_to') && $request->leave_date_to != '') {
            $query->whereDate('leave_date_to', '<=', $request->leave_date_to);
        }

        $nikays = User::all();
        $items = $query->get();

        return view('departments.deptleavedetail', compact('items', 'nikays'));
    }


    // public function deptsalarydetail(Request $request)
    // {
    //     $query = SalaryDetail::query();

    //     if ($request->has('salary_number_regular') && $request->salary_number_regular != '') {
    //         $salaryNumber = $request->salary_number_regular;
    //         $query->where('salary_number_regular', 'like', '%' . $salaryNumber . '%');
    //     }
    //     if ($request->has('ulb_name') && $request->ulb_name != '') {
    //         $ulbName = $request->ulb_name;
    //         $query->whereHas('ulb', function ($q) use ($ulbName) {
    //             $q->where('name', 'like', '%' . $ulbName . '%');
    //         });
    //     }
    //     if ($request->has('from_date') && $request->has('to_date')) {
    //         $fromDate = $request->from_date;
    //         $toDate = $request->to_date;

    //         if (!empty($fromDate) && !empty($toDate)) {
    //             $query->whereBetween('updated_at', [$fromDate . ' 00:00:00', $toDate . ' 23:59:59']);
    //         }
    //     }
    //     $queryone = SalaryDetailPlacement::query();

    //     if ($request->has('salary_number_placement') && $request->salary_number_placement != '') {
    //         $salaryNumber = $request->salary_number_placement;
    //         $queryone->where('salary_number_placement', 'like', '%' . $salaryNumber . '%');
    //     }
    //     if ($request->has('ulb_name') && $request->ulb_name != '') {
    //         $ulbName = $request->ulb_name;
    //         $queryone->whereHas('ulb', function ($q) use ($ulbName) {
    //             $q->where('name', 'like', '%' . $ulbName . '%');
    //         });
    //     }
    //     if ($request->has('from_date') && $request->has('to_date')) {
    //         $fromDate = $request->from_date;
    //         $toDate = $request->to_date;

    //         if (!empty($fromDate) && !empty($toDate)) {
    //             $queryone->whereBetween('updated_at', [$fromDate . ' 00:00:00', $toDate . ' 23:59:59']);
    //         }
    //     }
    //     // Get the filtered salary details
    //     $items = $query->get();
    //     $salary = $queryone->get();
    //     $nikays = User::all();
    //     return view('departments.deptsalarydetail', compact('nikays', 'items', 'salary'));
    // }
    public function deptsalarydetail(Request $request)
    {
        $nikays = User::all();

        // If no form_type in request, load all data for both tables (initial load)
        if (!$request->has('form_type')) {
            $items = SalaryDetail::all();
            $salary = SalaryDetailPlacement::all();
        } else if ($request->form_type === 'regular') {
            $query = SalaryDetail::query();

            if ($request->filled('salary_number_regular')) {
                $query->where('salary_number_regular', 'like', '%' . $request->salary_number_regular . '%');
            }
            if ($request->filled('ulb_name')) {
                $query->whereHas('ulb', function ($q) use ($request) {
                    $q->where('name', 'like', '%' . $request->ulb_name . '%');
                });
            }
            if ($request->filled('from_date') && $request->filled('to_date')) {
                $query->whereBetween('updated_at', [$request->from_date . ' 00:00:00', $request->to_date . ' 23:59:59']);
            }

            $items = $query->get();
            $salary = collect(); // empty collection so placement table won’t show
        } else if ($request->form_type === 'placement') {
            $query = SalaryDetailPlacement::query();

            if ($request->filled('salary_number_placement')) {
                $query->where('salary_number_placement', 'like', '%' . $request->salary_number_placement . '%');
            }
            if ($request->filled('ulb_name')) {
                $query->whereHas('ulb', function ($q) use ($request) {
                    $q->where('name', 'like', '%' . $request->ulb_name . '%');
                });
            }
            if ($request->filled('from_date') && $request->filled('to_date')) {
                $query->whereBetween('updated_at', [$request->from_date . ' 00:00:00', $request->to_date . ' 23:59:59']);
            }

            $salary = $query->get();
            $items = collect(); // empty collection so regular table won’t show
        }

        return view('departments.deptsalarydetail', compact('nikays', 'items', 'salary'));
    }


    public function depteletricitybilldetail(Request $request)
    {

        $query = ElectricityBill::query();

        if ($request->has('electricity_bill_no') && $request->electricity_bill_no != '') {
            $electricityBillNo = $request->electricity_bill_no;
            // Filter based on Electricity Bill No
            $query->where('electricity_bill_no', 'like', '%' . $electricityBillNo . '%');
        }
        if ($request->has('ulb_name') && $request->ulb_name != '') {
            $ulbName = $request->ulb_name;
            $query->whereHas('ulb', function ($q) use ($ulbName) {
                $q->where('name', 'like', '%' . $ulbName . '%');
            });
        }
        if ($request->has('from_date') && $request->has('to_date')) {
            $fromDate = $request->from_date;
            $toDate = $request->to_date;

            if (!empty($fromDate) && !empty($toDate)) {
                $query->whereBetween('updated_at', [$fromDate . ' 00:00:00', $toDate . ' 23:59:59']);
            }
        }

        $items = $query->get();
        $nikays = User::all();
        return view('departments.deptelectricitybilldetail', compact('items', 'nikays'));
    }
    public function deptelectricityyearlydata(Request $request)
    {

        $query = ElectricityYearlyData::query();
        $items = ElectricityYearlyData::select('*')
            ->whereIn('id', function ($query) {
                $query->selectRaw('MAX(id)')
                    ->from('electricity_yearly_data')
                    ->groupBy('ulb_id');
            })
            ->get();
        $yearlymodal = ElectricityYearlyData::with('ulb')->get()->groupBy('ulb_id');
        // Fetch all ULBs (users)
        $nikays = User::all();

        return view('departments.deptelectricityyearlydata', compact('items', 'nikays', 'yearlymodal'));
    }

    // public function deptincomeexpenditurereport()
    // {
    //     // Grouping by ulb_id and counting total entries for each
    //     $items = IncomeExpenditure::select('ulb_id', DB::raw('count(*) as total'))
    //         ->groupBy('ulb_id')
    //         ->get();

    //     $nikays = User::all();

    //     return view('departments.deptincomeexpenditure', compact('items', 'nikays'));
    // }
    // public function inconeexpendituredetail($ulbId)
    // {
    //     $entries = IncomeExpenditure::where('ulb_id', $ulbId)->get();

    //     return view('departments.inconeexpendituredetail', compact('entries', 'ulbId'));
    // }

    // public function incomeexpenditurereport(Request $request)
    // {
    //     $month = $request->input('month'); // Format: yyyy-mm

    //     $groupedData = $query = IncomeExpenditure::select(
    //         'ulb_id',
    //         DB::raw('SUM(total_income) as total_income'),
    //         DB::raw('SUM(total_expenditure) as total_expenditure'),
    //         DB::raw('SUM(total_income) - SUM(total_expenditure) as balance_amount'),
    //         DB::raw('SUM(monthly_chungi) as monthly_chungi'),
    //         DB::raw('SUM(utpadkar) as utpadkar'),
    //         DB::raw('SUM(yatrikar) as yatrikar'),
    //         DB::raw('SUM(bar_license) as bar_license'),
    //         DB::raw('SUM(mudrank_shulk) as mudrank_shulk'),
    //         DB::raw('SUM(permanent_employee_salary) as permanent_employee_salary'),
    //         DB::raw('SUM(placement_employee_salary) as placement_employee_salary'),
    //         DB::raw('SUM(padhadhikari_mandeya) as padhadhikari_mandeya'),
    //         DB::raw('SUM(gpf_cpf) as gpf_cpf'),
    //         DB::raw('SUM(travel_allowance) as travel_allowance'),
    //         DB::raw('SUM(avkash_nakdikaran) as avkash_nakdikaran'),
    //         DB::raw('SUM(electricity_bill) as electricity_bill'),
    //         DB::raw('SUM(diesal_petrol) as diesal_petrol'),
    //         DB::raw('SUM(telephone_stationary) as telephone_stationary')
    //     )
    //         ->with('ulb.district')
    //         ->when($month, function ($q) use ($month) {
    //             $q->where('month', $month);
    //         })
    //         ->groupBy('ulb_id')
    //         ->get()
    //         ->map(function ($item) use ($month) {
    //             $remark = IncomeExpenditure::where('ulb_id', $item->ulb_id)
    //                 ->when($month, function ($q) use ($month) {
    //                     $q->where('month', $month);
    //                 }, function ($q) {
    //                     $q->orderByDesc('updated_at');
    //                 })
    //                 ->value('remark');

    //             $item->remark = $remark;
    //             return $item;
    //         });

    //     return view('departments.incomeexpenditurereport', compact('groupedData'));
    // }


    public function incomeexpenditurereport(Request $request)
    {
        $month = $request->input('month');
        $ulbName = $request->input('ulb_name');

        $query = IncomeExpenditure::select(
            'ulb_id',
            DB::raw('SUM(total_income) as total_income'),
            DB::raw('SUM(total_expenditure) as total_expenditure'),
            DB::raw('SUM(total_income) - SUM(total_expenditure) as balance_amount'),
            DB::raw('SUM(monthly_chungi) as monthly_chungi'),
            DB::raw('SUM(utpadkar) as utpadkar'),
            DB::raw('SUM(yatrikar) as yatrikar'),
            DB::raw('SUM(bar_license) as bar_license'),
            DB::raw('SUM(mudrank_shulk) as mudrank_shulk'),
            DB::raw('SUM(own_revenue) as own_revenue'),
            DB::raw('SUM(anya_madh) as anya_madh'),
            DB::raw('SUM(permanent_employee_salary) as permanent_employee_salary'),
            DB::raw('SUM(placement_employee_salary) as placement_employee_salary'),
            DB::raw('SUM(padhadhikari_mandeya) as padhadhikari_mandeya'),
            DB::raw('SUM(gpf_cpf) as gpf_cpf'),
            DB::raw('SUM(travel_allowance) as travel_allowance'),
            DB::raw('SUM(avkash_nakdikaran) as avkash_nakdikaran'),
            DB::raw('SUM(electricity_bill) as electricity_bill'),
            DB::raw('SUM(diesal_petrol) as diesal_petrol'),
            DB::raw('SUM(telephone_stationary) as telephone_stationary'),
            DB::raw('SUM(arrears) as arrears'),
            DB::raw('SUM(advertising_expense) as advertising_expense'),
            DB::raw('SUM(vividh_vyay) as vividh_vyay'),
            DB::raw('SUM(jalkarya_sandharan) as jalkarya_sandharan'),
            DB::raw('SUM(bhawan_anughya_rashi_bhugtan) as bhawan_anughya_rashi_bhugtan'),
            DB::raw('SUM(freehold_madh_vyay) as freehold_madh_vyay'),
            DB::raw('SUM(nikay_nidhi_nirman_karya_vyay) as nikay_nidhi_nirman_karya_vyay')
        )
            ->with('ulb.district');

        // Filter by month if provided
        if (!empty($month)) {
            $query->where('month', $month);
        }

        // Filter by ulb_name if provided
        if (!empty($ulbName)) {
            $query->whereHas('ulb', function ($q) use ($ulbName) {
                $q->where('name', 'like', '%' . $ulbName . '%');
            });
        }

        // Group and get results
        $groupedData = $query->groupBy('ulb_id')->get()->map(function ($item) use ($month) {
            $remark = IncomeExpenditure::where('ulb_id', $item->ulb_id)
                ->when($month, function ($q) use ($month) {
                    $q->where('month', $month);
                }, function ($q) {
                    $q->orderByDesc('updated_at');
                })
                ->value('remark');

            $item->remark = $remark;
            return $item;
        });

        // Get all nikays (ULBs) for dropdown
        $nikays = User::all();

        return view('departments.incomeexpenditurereport', compact('groupedData', 'nikays'));
    }





    public function deptnewmeterdetail(Request $request)
    {
        // Start with the base query
        $query = NewMeterRegistration::query();
        if ($request->has('meter_registrtaion_no') && $request->meter_registrtaion_no != '') {
            $uniqueMeterNo = $request->meter_registrtaion_no;
            $query->where('meter_registrtaion_no', 'like', '%' . $uniqueMeterNo . '%');
        }


        if ($request->has('ulb_name') && $request->ulb_name != '') {
            $ulbName = $request->ulb_name;

            $query->whereHas('ulb', function ($q) use ($ulbName) {
                $q->where('name', 'like', '%' . $ulbName . '%');
            });
        }
        if ($request->has('from_date') && $request->has('to_date')) {
            $fromDate = $request->from_date;
            $toDate = $request->to_date;

            if (!empty($fromDate) && !empty($toDate)) {
                $query->whereBetween('updated_at', [$fromDate . ' 00:00:00', $toDate . ' 23:59:59']);
            }
        }
        if ($request->has('meter_status') && !empty($request->meter_status)) {
            $meterStatus = $request->meter_status;
            $query->where('meter_status', $meterStatus);
        }

        $items = $query->get();

        $nikays = User::all();
        return view('departments.deptnewmeterdetail', compact('items', 'nikays'));
    }

    public function deptSuspensionReport(Request $request)
    {

        $suspension = Employee::whereNotNull('suspension_date')
            ->whereNull('bahal_date')
            ->get();
        $item = Employee::whereNotNull('bahal_date')
            ->get();
        $nikays = User::all();
        return view('departments/deptSuspensionReport', compact('item', 'suspension', 'nikays'));
    }

    public function deptulballotmantdetail(Request $request)
    {
        $items = ChungiMadhVyay::with('ulb')
            ->get()
            ->groupBy('ulb_id')
            ->map(function ($ulbGroup) {
                return $ulbGroup->sortByDesc('updated_at')->first();
            });

        $totalAmountReleased = [];
        foreach ($items as $item) {
            $ulbId = $item->ulb_id;
            // $totalAmountReleased[$ulbId] = ChungiMadhVyay::where('ulb_id', $ulbId)->sum('amount_released');
            $totalAmountReleased[$ulbId] = ChungiMadhVyay::where('ulb_id', $ulbId)
                ->whereNotNull('month')
                ->sum('amount_released');
        }
        $chungimodal = ChungiMadhVyay::with('ulb')->get()->groupBy('ulb_id');
        $appatnidhi = AppatnidhiMarammat::with('ulb')
            ->get()
            ->groupBy('ulb_id')
            ->map(function ($ulbGroup) {
                return $ulbGroup->sortByDesc('updated_at')->first();
            });

        $totalAmountReleasedtwo = [];
        foreach ($appatnidhi as $item) {
            $ulbId = $item->ulb_id;
            $totalAmountReleasedtwo[$ulbId] = AppatnidhiMarammat::where('ulb_id', $ulbId)->whereNotNull('month')
                ->sum('amount_released');
        }
        $appatnidhimodal = AppatnidhiMarammat::with('ulb')->get()->groupBy('ulb_id');

        $mahapaurnidhi = MahapaurAdhyakshNidhi::with('ulb')
            ->get()
            ->groupBy('ulb_id')
            ->map(function ($ulbGroup) {
                return $ulbGroup->sortByDesc('updated_at')->first();
            });

        $totalAmountReleasedthree = [];
        foreach ($mahapaurnidhi as $item) {
            $ulbId = $item->ulb_id;
            $totalAmountReleasedthree[$ulbId] = MahapaurAdhyakshNidhi::where('ulb_id', $ulbId)->whereNotNull('month')
                ->sum('amount_released');
        }
        $mahapaurnidhimodal = MahapaurAdhyakshNidhi::with('ulb')->get()->groupBy('ulb_id');
        // ParshadNidhi Total Amount Released
        $parshadnidhi = ParshadNidhi::with('ulb')
            ->get()
            ->groupBy('ulb_id')
            ->map(function ($ulbGroup) {
                return $ulbGroup->sortByDesc('updated_at')->first();
            });

        $totalAmountReleasedfour = [];
        foreach ($parshadnidhi as $item) {
            $ulbId = $item->ulb_id;
            $totalAmountReleasedfour[$ulbId] = ParshadNidhi::where('ulb_id', $ulbId)->whereNotNull('month')
                ->sum('amount_released');
        }
        $parshadnidhimodal = ParshadNidhi::with('ulb')->get()->groupBy('ulb_id');
        // SwachCommando Total Amount Released
        $swachcommando = SwachCommando::with('ulb')
            ->get()
            ->groupBy('ulb_id')
            ->map(function ($ulbGroup) {
                return $ulbGroup->sortByDesc('updated_at')->first();
            });

        $totalAmountReleasedfive = [];
        foreach ($swachcommando as $item) {
            $ulbId = $item->ulb_id;
            $totalAmountReleasedfive[$ulbId] = SwachCommando::where('ulb_id', $ulbId)->whereNotNull('month')
                ->sum('amount_released');
        }
        $swachcommandomodal = SwachCommando::with('ulb')->get()->groupBy('ulb_id');
        // MissionCleanCity Total Amount Released
        $cleancity = MissionCleanCity::with('ulb')
            ->get()
            ->groupBy('ulb_id')
            ->map(function ($ulbGroup) {
                return $ulbGroup->sortByDesc('updated_at')->first();
            });


        $totalAmountReleasedsix = [];
        foreach ($cleancity as $item) {
            $ulbId = $item->ulb_id;
            $totalAmountReleasedsix[$ulbId] = MissionCleanCity::where('ulb_id', $ulbId)->whereNotNull('month')
                ->sum('amount_released');
        }
        $cleancitymodal = MissionCleanCity::with('ulb')->get()->groupBy('ulb_id');
        // LambitVetan Total Amount Released
        $lambitvetan = LambitVetan::with('ulb')
            ->get()
            ->groupBy('ulb_id')
            ->map(function ($ulbGroup) {
                return $ulbGroup->sortByDesc('updated_at')->first();
            });

        $totalAmountReleasedseven = [];
        foreach ($lambitvetan as $item) {
            $ulbId = $item->ulb_id;
            $totalAmountReleasedseven[$ulbId] = LambitVetan::where('ulb_id', $ulbId)->whereNotNull('month')
                ->sum('amount_released');
        }
        $lambitvetanmodal = LambitVetan::with('ulb')->get()->groupBy('ulb_id');
        // AnyaKarya Total Amount Released
        $anyakarya = AnyaKarya::with('ulb')
            ->get()
            ->groupBy('ulb_id')
            ->map(function ($ulbGroup) {
                return $ulbGroup->sortByDesc('updated_at')->first();
            });

        $totalAmountReleasedeight = [];
        foreach ($anyakarya as $item) {
            $ulbId = $item->ulb_id;
            $totalAmountReleasedeight[$ulbId] = AnyaKarya::where('ulb_id', $ulbId)->whereNotNull('month')
                ->sum('amount_released');
        }
        $anyakaryamodal = AnyaKarya::with('ulb')->get()->groupBy('ulb_id');
        // PeyjalkastNivaran Total Amount Released
        $peyjalnivaran = PeyjalkastNivaran::with('ulb')
            ->get()
            ->groupBy('ulb_id')
            ->map(function ($ulbGroup) {
                return $ulbGroup->sortByDesc('updated_at')->first();
            });

        $totalAmountReleasednine = [];
        foreach ($peyjalnivaran as $item) {
            $ulbId = $item->ulb_id;
            $totalAmountReleasednine[$ulbId] = PeyjalkastNivaran::where('ulb_id', $ulbId)->whereNotNull('month')
                ->sum('amount_released');
        }
        $peyjalnivaranmodal = PeyjalkastNivaran::with('ulb')->get()->groupBy('ulb_id');

        // JalPradayGirh Total Amount Released
        $jalpradaygirh = JalPradayGirh::with('ulb')
            ->get()
            ->groupBy('ulb_id')
            ->map(function ($ulbGroup) {
                return $ulbGroup->sortByDesc('updated_at')->first();
            });

        $totalAmountReleasedten = [];
        foreach ($jalpradaygirh as $item) {
            $ulbId = $item->ulb_id;
            $totalAmountReleasedten[$ulbId] = JalPradayGirh::where('ulb_id', $ulbId)->whereNotNull('month')
                ->sum('amount_released');
        }
        $jalpradaygirhmodal = JalPradayGirh::with('ulb')->get()->groupBy('ulb_id');
        // MudrankSulak Total Amount Released
        $mudranksulak = MudrankSulak::with('ulb')
            ->get()
            ->groupBy('ulb_id')
            ->map(function ($ulbGroup) {
                return $ulbGroup->sortByDesc('updated_at')->first();
            });

        $totalAmountReleasedeleven = [];
        foreach ($mudranksulak as $item) {
            $ulbId = $item->ulb_id;
            $totalAmountReleasedeleven[$ulbId] = MudrankSulak::where('ulb_id', $ulbId)->whereNotNull('month')
                ->sum('amount_released');
        }
        $mudranksulakmodal = MudrankSulak::with('ulb')->get()->groupBy('ulb_id');
        // Bar Total Amount Released
        $bar = Bar::with('ulb')
            ->get()
            ->groupBy('ulb_id')
            ->map(function ($ulbGroup) {
                return $ulbGroup->sortByDesc('updated_at')->first();
            });

        $totalAmountReleasedtwelve = [];
        foreach ($bar as $item) {
            $ulbId = $item->ulb_id;
            $totalAmountReleasedtwelve[$ulbId] = Bar::where('ulb_id', $ulbId)->whereNotNull('month')
                ->sum('amount_released');
        }
        $barmodal = Bar::with('ulb')->get()->groupBy('ulb_id');
        // YatriKar Total Amount Released
        $yatrikar = YatriKar::with('ulb')
            ->get()
            ->groupBy('ulb_id')
            ->map(function ($ulbGroup) {
                return $ulbGroup->sortByDesc('updated_at')->first();
            });

        $totalAmountReleasedthirteen = [];
        foreach ($yatrikar as $item) {
            $ulbId = $item->ulb_id;
            $totalAmountReleasedthirteen[$ulbId] = YatriKar::where('ulb_id', $ulbId)->whereNotNull('month')
                ->sum('amount_released');
        }
        $yatrikaranmodal = YatriKar::with('ulb')->get()->groupBy('ulb_id');
        // Utpadkar Total Amount Released
        $utpadkar = Utpadkar::with('ulb')
            ->get()
            ->groupBy('ulb_id')
            ->map(function ($ulbGroup) {
                return $ulbGroup->sortByDesc('updated_at')->first();
            });

        $totalAmountReleasedfourteen = [];
        foreach ($utpadkar as $item) {
            $ulbId = $item->ulb_id;
            $totalAmountReleasedfourteen[$ulbId] = Utpadkar::where('ulb_id', $ulbId)->whereNotNull('month')
                ->sum('amount_released');
        }
        $utpadkarmodal = Utpadkar::with('ulb')->get()->groupBy('ulb_id');

        return view('departments.deptulballotmantdetail', compact(
            // 'data',
            'chungimodal',
            'appatnidhimodal',
            'mahapaurnidhimodal',
            'parshadnidhimodal',
            'swachcommandomodal',
            'cleancitymodal',
            'lambitvetanmodal',
            'anyakaryamodal',
            'peyjalnivaranmodal',
            'jalpradaygirhmodal',
            'mudranksulakmodal',
            'barmodal',
            'yatrikaranmodal',
            'utpadkarmodal',
            'totalAmountReleased',
            'totalAmountReleasedtwo',
            'totalAmountReleasedthree',
            'totalAmountReleasedfour',
            'totalAmountReleasedfive',
            'totalAmountReleasedsix',
            'totalAmountReleasedseven',
            'totalAmountReleasedeight',
            'totalAmountReleasednine',
            'totalAmountReleasedten',
            'totalAmountReleasedeleven',
            'totalAmountReleasedtwelve',
            'totalAmountReleasedthirteen',
            'totalAmountReleasedfourteen',
            'items',
            'appatnidhi',
            'mahapaurnidhi',
            'parshadnidhi',
            'swachcommando',
            'cleancity',
            'lambitvetan',
            'anyakarya',
            'peyjalnivaran',
            'jalpradaygirh',
            'mudranksulak',
            'bar',
            'yatrikar',
            'utpadkar'
        ));
    }

    public function ulballotmentform()
    {
        $nikay = Auth::guard('web')->user();
        $ulbs = User::all();
        $item = ChungiMadhVyay::get();
        $appatnidhi = AppatnidhiMarammat::get();
        $mahapaurnidhi = MahapaurAdhyakshNidhi::get();
        $parshadnidhi = ParshadNidhi::get();

        return view('departments/ulballotmentsform', compact('item', 'appatnidhi', 'mahapaurnidhi', 'parshadnidhi', 'ulbs', 'nikay'));
    }
    public function ulballotmentformsubmit(Request $request)
    {
        $chungi_no = mt_rand(1000, 9999);
        $nikay = Auth::guard('web')->user();
        $request->validate(
            [
                'ulb_id' => 'required',
                'month' => 'required',
                'amount_released' => 'required',
                'remark' => 'required',

            ],
        );
        $data = new ChungiMadhVyay();
        $data->ulb_id = $request->input('ulb_id');
        $data->month = $request->input('month');
        $data->chungi_no = $chungi_no;
        $data->amount_released = $request->input('amount_released');
        $data->remark = $request->input('remark');
        $data->save();
        Alert::success('Success!', 'Data submitted successfully');
        return back();
    }
    public function appatnidhisubmit(Request $request)
    {
        $appatnidhi_unique_no = mt_rand(1000, 9999);
        $nikay = Auth::guard('web')->user();
        $request->validate(
            [
                'ulb_id' => 'required',
                'month' => 'required',
                'amount_released' => 'required',
                'remark' => 'required',

            ],
        );
        $data = new AppatnidhiMarammat();
        $data->ulb_id = $request->input('ulb_id');
        $data->month = $request->input('month');
        $data->appatnidhi_unique_no = $appatnidhi_unique_no;
        $data->amount_released = $request->input('amount_released');
        $data->remark = $request->input('remark');
        $data->save();
        Alert::success('Success!', 'Data submitted successfully');
        return back();
    }
    public function mahapaurnidhisubmit(Request $request)
    {
        $unique_no = mt_rand(1000, 9999);
        $nikay = Auth::guard('web')->user();
        $request->validate(
            [
                'ulb_id' => 'required',
                'month' => 'required',
                'amount_released' => 'required',
                'remark' => 'required',

            ],
        );
        $data = new MahapaurAdhyakshNidhi();
        $data->ulb_id = $request->input('ulb_id');
        $data->month = $request->input('month');
        $data->unique_no = $unique_no;
        $data->amount_released = $request->input('amount_released');
        $data->remark = $request->input('remark');
        $data->save();
        Alert::success('Success!', 'Data submitted successfully');
        return back();
    }
    public function parshadnidhisubmit(Request $request)
    {
        $unique_no = mt_rand(1000, 9999);
        $nikay = Auth::guard('web')->user();
        $request->validate(
            [
                'ulb_id' => 'required',
                'month' => 'required',
                'amount_released' => 'required',
                'remark' => 'required',

            ],
        );
        $data = new ParshadNidhi();
        $data->ulb_id = $request->input('ulb_id');
        $data->month = $request->input('month');
        $data->unique_no = $unique_no;
        $data->amount_released = $request->input('amount_released');
        $data->remark = $request->input('remark');
        $data->save();
        Alert::success('Success!', 'Data submitted successfully');
        return back();
    }

    public function swachcommandosubmit(Request $request)
    {
        $unique_no = mt_rand(1000, 9999);
        $nikay = Auth::guard('web')->user();
        $request->validate(
            [
                'ulb_id' => 'required',
                'month' => 'required',
                'amount_released' => 'required',
                'remark' => 'required',

            ],
        );
        $data = new SwachCommando();
        $data->ulb_id = $request->input('ulb_id');
        $data->month = $request->input('month');
        $data->unique_no = $unique_no;
        $data->amount_released = $request->input('amount_released');
        $data->remark = $request->input('remark');
        $data->save();
        Alert::success('Success!', 'Data submitted successfully');
        return back();
    }
    public function cleancitysubmit(Request $request)
    {
        $unique_no = mt_rand(1000, 9999);
        $nikay = Auth::guard('web')->user();
        $request->validate(
            [
                'ulb_id' => 'required',
                'month' => 'required',
                'amount_released' => 'required',
                'remark' => 'required',

            ],
        );
        $data = new MissionCleanCity();
        $data->ulb_id = $request->input('ulb_id');
        $data->month = $request->input('month');
        $data->unique_no = $unique_no;
        $data->amount_released = $request->input('amount_released');
        $data->remark = $request->input('remark');
        $data->save();
        Alert::success('Success!', 'Data submitted successfully');
        return back();
    }
    public function lambitvetansubmit(Request $request)
    {
        $unique_no = mt_rand(1000, 9999);
        $nikay = Auth::guard('web')->user();
        $request->validate(
            [
                'ulb_id' => 'required',
                'month' => 'required',
                'amount_released' => 'required',
                'remark' => 'required',

            ],
        );
        $data = new LambitVetan();
        $data->ulb_id = $request->input('ulb_id');
        $data->month = $request->input('month');
        $data->unique_no = $unique_no;
        $data->amount_released = $request->input('amount_released');
        $data->remark = $request->input('remark');
        $data->save();
        Alert::success('Success!', 'Data submitted successfully');
        return back();
    }
    public function anyakaryasubmit(Request $request)
    {
        $unique_no = mt_rand(1000, 9999);
        $nikay = Auth::guard('web')->user();
        $request->validate(
            [
                'ulb_id' => 'required',
                'month' => 'required',
                'amount_released' => 'required',
                'remark' => 'required',

            ],
        );
        $data = new AnyaKarya();
        $data->ulb_id = $request->input('ulb_id');
        $data->month = $request->input('month');
        $data->unique_no = $unique_no;
        $data->amount_released = $request->input('amount_released');
        $data->remark = $request->input('remark');
        $data->save();
        Alert::success('Success!', 'Data submitted successfully');
        return back();
    }
    public function peyjalnivaransubmit(Request $request)
    {
        $unique_no = mt_rand(1000, 9999);
        $nikay = Auth::guard('web')->user();
        $request->validate(
            [
                'ulb_id' => 'required',
                'month' => 'required',
                'amount_released' => 'required',
                'remark' => 'required',

            ],
        );
        $data = new PeyjalkastNivaran();
        $data->ulb_id = $request->input('ulb_id');
        $data->month = $request->input('month');
        $data->unique_no = $unique_no;
        $data->amount_released = $request->input('amount_released');
        $data->remark = $request->input('remark');
        $data->save();
        Alert::success('Success!', 'Data submitted successfully');
        return back();
    }
    public function jalpradaygirhsubmit(Request $request)
    {
        $unique_no = mt_rand(1000, 9999);
        $nikay = Auth::guard('web')->user();
        $request->validate(
            [
                'ulb_id' => 'required',
                'month' => 'required',
                'amount_released' => 'required',
                'remark' => 'required',

            ],
        );
        $data = new JalPradayGirh();
        $data->ulb_id = $request->input('ulb_id');
        $data->month = $request->input('month');
        $data->unique_no = $unique_no;
        $data->amount_released = $request->input('amount_released');
        $data->remark = $request->input('remark');
        $data->save();
        Alert::success('Success!', 'Data submitted successfully');
        return back();
    }
    public function mudranksulaksubmit(Request $request)
    {
        $unique_no = mt_rand(1000, 9999);
        $nikay = Auth::guard('web')->user();
        $request->validate(
            [
                'ulb_id' => 'required',
                'month' => 'required',
                'amount_released' => 'required',
                'remark' => 'required',

            ],
        );
        $data = new MudrankSulak();
        $data->ulb_id = $request->input('ulb_id');
        $data->month = $request->input('month');
        $data->unique_no = $unique_no;
        $data->amount_released = $request->input('amount_released');
        $data->remark = $request->input('remark');
        $data->save();
        Alert::success('Success!', 'Data submitted successfully');
        return back();
    }
    public function barsubmit(Request $request)
    {
        $unique_no = mt_rand(1000, 9999);
        $nikay = Auth::guard('web')->user();
        $request->validate(
            [
                'ulb_id' => 'required',
                'month' => 'required',
                'amount_released' => 'required',
                'remark' => 'required',

            ],
        );
        $data = new Bar();
        $data->ulb_id = $request->input('ulb_id');
        $data->month = $request->input('month');
        $data->unique_no = $unique_no;
        $data->amount_released = $request->input('amount_released');
        $data->remark = $request->input('remark');
        $data->save();
        Alert::success('Success!', 'Data submitted successfully');
        return back();
    }
    public function yatrikarsubmit(Request $request)
    {
        $unique_no = mt_rand(1000, 9999);
        $nikay = Auth::guard('web')->user();
        $request->validate(
            [
                'ulb_id' => 'required',
                'month' => 'required',
                'amount_released' => 'required',
                'remark' => 'required',

            ],
        );
        $data = new YatriKar();
        $data->ulb_id = $request->input('ulb_id');
        $data->month = $request->input('month');
        $data->unique_no = $unique_no;
        $data->amount_released = $request->input('amount_released');
        $data->remark = $request->input('remark');
        $data->save();
        Alert::success('Success!', 'Data submitted successfully');
        return back();
    }
    public function utpadkarsubmit(Request $request)
    {
        $unique_no = mt_rand(1000, 9999);
        $nikay = Auth::guard('web')->user();
        $request->validate(
            [
                'ulb_id' => 'required',
                'month' => 'required',
                'amount_released' => 'required',
                'remark' => 'required',

            ],
        );
        $data = new Utpadkar();
        $data->ulb_id = $request->input('ulb_id');
        $data->month = $request->input('month');
        $data->unique_no = $unique_no;
        $data->amount_released = $request->input('amount_released');
        $data->remark = $request->input('remark');
        $data->save();
        Alert::success('Success!', 'Data submitted successfully');
        return back();
    }
    public function uploadexcel(Request $request)
    {
        $request->validate([
            'file' => 'required|mimes:xlsx,xls,csv|max:2048',
        ]);
        Excel::import(new DataImport, $request->file('file'));

        Alert::success('Success!', 'Chungi Data Updated successfully');
        return back();
    }
    public function uploadexcelappatnidhi(Request $request)
    {
        $request->validate([
            'file' => 'required|mimes:xlsx,xls,csv|max:2048',
        ]);
        Excel::import(new DataimportAppatnidhi, $request->file('file'));

        Alert::success('Success!', 'Appatnidhi Data Updated Successfully');
        return back();
    }
    public function uploadexcelMahapaur(Request $request)
    {
        $request->validate([
            'file' => 'required|mimes:xlsx,xls,csv|max:2048',
        ]);
        Excel::import(new DataimportMahapaur, $request->file('file'));

        Alert::success('Success!', 'Data Updated Successfully');
        return back();
    }
    public function uploadexcelParshadnidhi(Request $request)
    {
        $request->validate([
            'file' => 'required|mimes:xlsx,xls,csv|max:2048',
        ]);
        Excel::import(new DataimportParshadnidhi, $request->file('file'));

        Alert::success('Success!', 'Data Updated Successfully');
        return back();
    }
    public function uploadexcelSwachcommando(Request $request)
    {
        $request->validate([
            'file' => 'required|mimes:xlsx,xls,csv|max:2048',
        ]);
        Excel::import(new DataimportSwachcommando, $request->file('file'));

        Alert::success('Success!', 'Data Updated Successfully');
        return back();
    }
    public function uploadexcelMissioncleancity(Request $request)
    {
        $request->validate([
            'file' => 'required|mimes:xlsx,xls,csv|max:2048',
        ]);
        Excel::import(new DataimportMissioncleancity, $request->file('file'));

        Alert::success('Success!', 'Data Updated Successfully');
        return back();
    }
    public function uploadexcelLambitVetan(Request $request)
    {
        $request->validate([
            'file' => 'required|mimes:xlsx,xls,csv|max:2048',
        ]);
        Excel::import(new DataimportLambitVetan, $request->file('file'));

        Alert::success('Success!', 'Data Updated Successfully');
        return back();
    }
    public function uploadexcelAnyaKarya(Request $request)
    {
        $request->validate([
            'file' => 'required|mimes:xlsx,xls,csv|max:2048',
        ]);
        Excel::import(new DataimportAnyaKarya, $request->file('file'));

        Alert::success('Success!', 'Data Updated Successfully');
        return back();
    }
    public function uploadexcelPeyjalkast(Request $request)
    {
        $request->validate([
            'file' => 'required|mimes:xlsx,xls,csv|max:2048',
        ]);
        Excel::import(new DataimportPeyjalkast, $request->file('file'));

        Alert::success('Success!', 'Data Updated Successfully');
        return back();
    }
    public function uploadexcelJalPradaygirh(Request $request)
    {
        $request->validate([
            'file' => 'required|mimes:xlsx,xls,csv|max:2048',
        ]);
        Excel::import(new DataimportJalpradgirh, $request->file('file'));

        Alert::success('Success!', 'Data Updated Successfully');
        return back();
    }
    public function uploadexcelMudranksulk(Request $request)
    {
        $request->validate([
            'file' => 'required|mimes:xlsx,xls,csv|max:2048',
        ]);
        Excel::import(new DataimportMudrankSulk, $request->file('file'));

        Alert::success('Success!', 'Data Updated Successfully');
        return back();
    }
    public function uploadexcelBar(Request $request)
    {
        $request->validate([
            'file' => 'required|mimes:xlsx,xls,csv|max:2048',
        ]);
        Excel::import(new DataimportBar, $request->file('file'));

        Alert::success('Success!', 'Data Updated Successfully');
        return back();
    }
    public function uploadexcelYatrikar(Request $request)
    {
        $request->validate([
            'file' => 'required|mimes:xlsx,xls,csv|max:2048',
        ]);
        Excel::import(new Dataimportyatrikar, $request->file('file'));

        Alert::success('Success!', 'Data Updated Successfully');
        return back();
    }
    public function uploadexcelUtapadkar(Request $request)
    {
        $request->validate([
            'file' => 'required|mimes:xlsx,xls,csv|max:2048',
        ]);
        Excel::import(new DataimportUtpadkar, $request->file('file'));

        Alert::success('Success!', 'Data Updated Successfully');
        return back();
    }

    public function ulbList()
    {

        $nikay = User::get();
        $division = MasterDivision::get();
        $district = MasterDistrict::get();
        return view('departments/ulblist', compact('nikay', 'division', 'district'));
    }


    public function adminUlbPasswordUpdate(Request $request, $id)
    {
        // Validate incoming request data
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'eng_name' => 'required|string|max:255',
            'email' => 'required|string|max:255',
            'division_id' => 'required|integer',
            'district_id' => 'required|integer',
            'ulb_type' => 'required|string|max:255',
            'office_address' => 'required|string|max:255',
            'cmo_name' => 'required|string|max:255',
            'cmo_mobile' => 'required|digits:10',
            'new_password' => 'nullable|string|min:6|confirmed',
        ]);

        // Fetch the ULB registration instance by ID
        $ulbRegistration = User::find($id);
        if (!$ulbRegistration) {
            return back()->withErrors(['ulb' => 'ULB not found'])->withInput();
        }

        // Update fields
        $ulbRegistration->name = $validatedData['name'];
        $ulbRegistration->eng_name = $validatedData['eng_name'];
        $ulbRegistration->email = $validatedData['email'];
        $ulbRegistration->division_id = $validatedData['division_id'];
        $ulbRegistration->district_id = $validatedData['district_id'];
        $ulbRegistration->ulb_type = $validatedData['ulb_type'];
        $ulbRegistration->office_address = $validatedData['office_address'];
        $ulbRegistration->cmo_name = $validatedData['cmo_name'];
        $ulbRegistration->cmo_mobile = $validatedData['cmo_mobile'];
        $ulbRegistration->password = Hash::make($validatedData['new_password']);

        // Save updated data
        try {
            $ulbRegistration->save();
        } catch (\Exception $e) {
            return back()->withErrors(['save' => 'Failed to update profile: ' . $e->getMessage()])->withInput();
        }
        // dd($ulbRegistration);
        Alert::success('success', 'Profile updated successfully');
        return back();
    }
    public function addulb()
    {


        $division = MasterDivision::get();
        $district = MasterDistrict::get();
        return view('departments/addulb', compact('division', 'district'));
    }
    public function addulbsubmit(Request $request)
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'eng_name' => 'required|string|max:255',
            'email' => 'required|string|max:255',
            'division_id' => 'required|integer',
            'district_id' => 'required|integer',
            'ulb_type' => 'required|string|max:255',
            'office_address' => 'required|string|max:255',
            'cmo_name' => 'required|string|max:255',
            'cmo_mobile' => 'required|digits:10',
            'new_password' => 'required|string|min:6|confirmed',
        ]);

        $lastUlb = User::orderBy('id', 'desc')->first();
        $lastUlbCode = $lastUlb ? $lastUlb->ulbidcode : 'UAD0000'; // Default if no records exist

        // 🔹 Extract the numeric part and increment it
        preg_match('/\d+$/', $lastUlbCode, $matches);
        $newNumber = isset($matches[0]) ? (int)$matches[0] + 1 : 1;
        $newUlbidcode = 'Uad' . str_pad($newNumber, 4, '0', STR_PAD_LEFT);

        $data = new User();
        $data->ulbidcode = $newUlbidcode; // Assign new ULB ID Code
        $data->name = $request->input('name');
        $data->eng_name = $request->input('eng_name');
        // $data->post = $request->input('post');
        $data->email = $request->input('email');
        $data->division_id = $request->input('division_id');
        $data->district_id = $request->input('district_id');
        $data->ulb_type = $request->input('ulb_type');
        $data->office_address = $request->input('office_address');
        $data->cmo_name = $request->input('cmo_name');
        $data->cmo_mobile = $request->input('cmo_mobile');
        $data->password =  Hash::make($request['new_password']);
        $data->save();
        Alert::success('success', 'Data submitted successfully');

        return back();
    }

    public function exportexcelulbwise()
    {
        // Fetch the required data
        $item = RevenueCollection::query();
        $item = $item->selectRaw('
            ulb_id,
            month,
            SUM(current_demand) as total_current_demand,
            SUM(current_collection) as total_current_collection,
            SUM(pending_demand) as total_pending_demand,
            SUM(pending_collection) as total_pending_collection ')
            ->groupBy('ulb_id', 'month')
            ->orderBy('month', 'asc')
            ->get();
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        $sheet->setCellValue('A1', 'Sno.');
        $sheet->setCellValue('B1', 'Month');
        $sheet->setCellValue('C1', 'Total Current Demand');
        $sheet->setCellValue('D1', 'Total Current Collection');
        $sheet->setCellValue('E1', 'Total Current Collection (%)');
        $sheet->setCellValue('F1', 'Total Pending Demand');
        $sheet->setCellValue('G1', 'Total Pending Collection');
        $sheet->setCellValue('H1', 'Total Pending Collection (%)');
        $sheet->setCellValue('I1', 'Total Demand');
        $sheet->setCellValue('J1', 'Total Collection');
        $sheet->setCellValue('K1', 'Total Collection (%)');

        $row = 2;

        foreach ($item as $index => $data) {
            $sheet->setCellValue('A' . $row, $index + 1); // Serial number
            $sheet->setCellValue('B' . $row, $data->month);
            $sheet->setCellValue('C' . $row, $data->total_current_demand);
            $sheet->setCellValue('D' . $row, $data->total_current_collection);
            $currentCollectionPercentage = $data->total_current_demand != 0
                ? ($data->total_current_collection / $data->total_current_demand) * 100
                : 0;
            $sheet->setCellValue('E' . $row, number_format($currentCollectionPercentage, 2));

            $sheet->setCellValue('F' . $row, $data->total_pending_demand);
            $sheet->setCellValue('G' . $row, $data->total_pending_collection);
            $pendingCollectionPercentage = $data->total_pending_demand != 0
                ? ($data->total_pending_collection / $data->total_pending_demand) * 100
                : 0;
            $sheet->setCellValue('H' . $row, number_format($pendingCollectionPercentage, 2));
            $totalDemand = $data->total_current_demand + $data->total_pending_demand;
            $totalCollection = $data->total_current_collection + $data->total_pending_collection;
            $sheet->setCellValue('I' . $row, $totalDemand);
            $sheet->setCellValue('J' . $row, $totalCollection);
            $totalCollectionPercentage = $totalDemand != 0
                ? ($totalCollection / $totalDemand) * 100
                : 0;
            $sheet->setCellValue('K' . $row, number_format($totalCollectionPercentage, 2));
            $row++; // Move to the next row
        }
        $writer = new Xlsx($spreadsheet);
        $filename = 'Ulb_detail_report' . '.xlsx';
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: max-age=0');
        $writer->save('php://output');
        exit;
    }


    public function exportexcelmonthwiserajasvasulli()
    {
        $revenueCollections = RevenueCollection::with('taxType')
            ->selectRaw('
                taxtype_id,
                month,
                SUM(current_demand) as current_demand,
                SUM(current_collection) as current_collection,
                SUM(pending_demand) as pending_demand,
                SUM(pending_collection) as pending_collection
            ')
            ->groupBy('taxtype_id', 'month')
            ->orderBy('month', 'asc')
            ->orderBy('taxtype_id', 'asc')
            ->get();

        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Headings
        $sheet->setCellValue('A1', 'Sno.');
        $sheet->setCellValue('B1', 'Tax Type');
        $sheet->setCellValue('C1', 'Month');
        $sheet->setCellValue('D1', 'Current Demand');
        $sheet->setCellValue('E1', 'Current Collection');
        $sheet->setCellValue('F1', 'Current Collection (%)');
        $sheet->setCellValue('G1', 'Pending Demand');
        $sheet->setCellValue('H1', 'Pending Collection');
        $sheet->setCellValue('I1', 'Pending Collection (%)');
        $sheet->setCellValue('J1', 'Total Demand');
        $sheet->setCellValue('K1', 'Total Collection');
        $sheet->setCellValue('L1', 'Total Collection (%)');

        $row = 2;
        foreach ($revenueCollections as $index => $data) {

            $sheet->setCellValue('A' . $row, (int)$index + 1);
            $sheet->setCellValue('B' . $row, optional($data->taxType)->tax_type);
            $sheet->setCellValue('C' . $row, $data->month);
            $sheet->setCellValue('D' . $row, $data->current_demand);
            $sheet->setCellValue('E' . $row, $data->current_collection);
            $currentPercentage = $data->current_demand != 0 ? ($data->current_collection / $data->current_demand) * 100 : 0;
            $sheet->setCellValue('F' . $row, number_format($currentPercentage, 2));
            $sheet->setCellValue('G' . $row, $data->pending_demand);
            $sheet->setCellValue('H' . $row, $data->pending_collection);
            $pendingPercentage = $data->pending_demand != 0 ? ($data->pending_collection / $data->pending_demand) * 100 : 0;
            $sheet->setCellValue('I' . $row, number_format($pendingPercentage, 2));
            $totalDemand = $data->current_demand + $data->pending_demand;
            $sheet->setCellValue('J' . $row, $totalDemand);
            $totalCollection = $data->current_collection + $data->pending_collection;
            $sheet->setCellValue('K' . $row, $totalCollection);
            $totalCollectionPercentage = $totalDemand != 0 ? ($totalCollection / $totalDemand) * 100 : 0;
            $sheet->setCellValue('L' . $row, number_format($totalCollectionPercentage, 2));

            $row++;
        }

        $writer = new Xlsx($spreadsheet);

        $filename = 'MonthWise_Rajasvasuli_Report_' . now()->format('YmdHis') . '.xlsx';
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        $writer->save('php://output');
        exit;
    }

    public function ExcelRajasvasuliDetailReport()
    {
        $revenueCollections = RevenueCollection::with('ulb', 'taxType')->get()
            ->groupBy(function ($item) {
                return $item->taxType->id . '-' . $item->month;
            })
            ->map(function ($collectionGroup) {
                return $collectionGroup->first();
            });
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Set headings
        $sheet->setCellValue('A1', 'Sno.');
        $sheet->setCellValue('B1', 'Ulb Name');
        $sheet->setCellValue('C1', 'Tax Type');
        $sheet->setCellValue('D1', 'Month');
        $sheet->setCellValue('E1', 'Current Demand');
        $sheet->setCellValue('F1', 'Current Collection');
        $sheet->setCellValue('G1', 'Current Collection (%)');
        $sheet->setCellValue('H1', 'Pending Demand');
        $sheet->setCellValue('I1', 'Pending Collection');
        $sheet->setCellValue('J1', 'Pending Collection (%)');
        $sheet->setCellValue('K1', 'Total Demand');
        $sheet->setCellValue('L1', 'Total Collection');
        $sheet->setCellValue('M1', 'Total Collection (%)');

        $row = 2;
        foreach ($revenueCollections as $index => $data) {

            $sheet->setCellValue('A' . $row, (int)$index + 1);
            $sheet->setCellValue('B' . $row, $data->ulb->name);
            $sheet->setCellValue('C' . $row, $data->taxType->tax_type);
            $sheet->setCellValue('D' . $row, $data->month);
            $sheet->setCellValue('E' . $row, $data->current_demand);
            $sheet->setCellValue('F' . $row, $data->current_collection);
            $currentPercentage = $data->current_demand != 0 ? ($data->current_collection / $data->current_demand) * 100 : 0;
            $sheet->setCellValue('G' . $row, number_format($currentPercentage, 2));
            $sheet->setCellValue('H' . $row, $data->pending_demand);
            $sheet->setCellValue('I' . $row, $data->pending_collection);
            $pendingPercentage = $data->pending_demand != 0 ? ($data->pending_collection / $data->pending_demand) * 100 : 0;
            $sheet->setCellValue('J' . $row, number_format($pendingPercentage, 2));
            $totalDemand = $data->current_demand + $data->pending_demand;
            $sheet->setCellValue('K' . $row, $totalDemand);
            $totalCollection = $data->current_collection + $data->pending_collection;
            $sheet->setCellValue('L' . $row, $totalCollection);
            $totalCollectionPercentage = $totalDemand != 0 ? ($totalCollection / $totalDemand) * 100 : 0;
            $sheet->setCellValue('M' . $row, number_format($totalCollectionPercentage, 2));

            $row++;  // Move to the next row
        }

        $writer = new Xlsx($spreadsheet);

        $filename = 'Rajasvasuli_Detail_Report_' .  '.xlsx';
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: max-age=0');
        $writer->save('php://output');
        exit;
    }

    public function exportexcelEmployee()
    {
        $ulbWiseCounts = Employee::get();
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        $sheet->setCellValue('A1', 'Sno.');
        $sheet->setCellValue('B1', 'Division name');
        $sheet->setCellValue('C1', 'वर्तमान निकाय का नाम');
        $sheet->setCellValue('D1', 'Previous Ulb name');
        $sheet->setCellValue('E1', 'Registration no');
        $sheet->setCellValue('F1', 'अधिकारी/कर्मचारी का नाम');
        $sheet->setCellValue('G1', 'पिता का नाम');
        $sheet->setCellValue('H1', 'जन्मतिथि');
        $sheet->setCellValue('I1', 'पदनाम');
        $sheet->setCellValue('J1', 'वेतनमान');
        $sheet->setCellValue('K1', 'Gender');
        $sheet->setCellValue('L1', 'शैक्षणिक योग्यता');
        $sheet->setCellValue('M1', 'श्रेणी');
        $sheet->setCellValue('N1', 'कर्मचारी/अधिकारी की सेवा स्वरूप');
        $sheet->setCellValue('O1', 'निकाय सेवा में प्रथम प्रविष्टि का दिनांक');
        $sheet->setCellValue('P1', 'मूल निकाय का नाम');
        $sheet->setCellValue('Q1', 'मूल विभाग');
        $sheet->setCellValue('R1', 'कार्यरत अधिकारी/कर्मचारी का वर्तमान मूल पदनाम');
        $sheet->setCellValue('S1', 'वर्तमान निकाय में कब से कार्यरत हैं');
        $sheet->setCellValue('T1', 'रिफ्रेन्स आईडी(आधार कार्ड नंबर)');
        $sheet->setCellValue('U1', 'मोबाइल नंबर');
        $sheet->setCellValue('V1', 'ईमेल आईडी');
        $sheet->setCellValue('W1', 'अस्थाई पता');
        $sheet->setCellValue('X1', 'स्थाई पता');
        $sheet->setCellValue('Y1', 'कुल शेष CL');
        $sheet->setCellValue('Z1', 'कुल शेष OL');
        $sheet->setCellValue('AA1', 'कुल शेष EL');
        $sheet->setCellValue('AB1', 'कुल शेष ML');
        $sheet->setCellValue('AC1', 'निलंबन तिथि');
        $sheet->setCellValue('AD1', 'निलंबन आदेश क्र');
        $sheet->setCellValue('AE1', 'Remark (निलंबन)');
        $sheet->setCellValue('AF1', 'बहाली तिथि');
        $sheet->setCellValue('AG1', 'बहाली आदेश क्र');
        $sheet->setCellValue('AH1', 'Remark (बहाली)');
        $sheet->setCellValue('AI1', 'e-HRMS मेल आईडी ');
        $sheet->setCellValue('AJ1', 'रिमार्क');

        $row = 2;

        foreach ($ulbWiseCounts as $index => $data) {
            $sheet->setCellValue('A' . $row, $index + 1);  // Sno.
            $sheet->setCellValue('B' . $row, $data->ulb->division->name);  // Division Name
            $sheet->setCellValue('C' . $row, $data->ulb->name);  // Current Ulb Name
            $sheet->setCellValue('D' . $row, $data->ulb->name);  // Previous Ulb Name
            $sheet->setCellValue('E' . $row, $data->registration_no);  // Registration No
            $sheet->setCellValue('F' . $row, $data->employee_name);  // Employee Name
            $sheet->setCellValue('G' . $row, $data->father_name);  // Post Name
            $sheet->setCellValue('H' . $row, $data->birth_date);  // Current Designation
            $sheet->setCellValue('I' . $row, $data->post->post_name);  // Payscale
            $sheet->setCellValue('J' . $row, $data->payscalename->payscale);  // Employee Type
            $sheet->setCellValue('K' . $row, $data->gender);  // Birth Date
            $sheet->setCellValue('L' . $row, $data->qualification);  // Joining Date
            $sheet->setCellValue('M' . $row, $data->caste);  // Qualification
            $sheet->setCellValue('N' . $row, $data->employeeType->type);  // Category (or your desired field name)
            $sheet->setCellValue('O' . $row, $data->joining_date);  // Service Status (or your desired field name)
            $sheet->setCellValue('P' . $row, $data->ulbnew->name);  // Entry Date (or your desired field name)
            $sheet->setCellValue('Q' . $row, $data->department_name);  // Original Ulb Name
            $sheet->setCellValue('R' . $row, $data->current_designation);  // Department Name
            $sheet->setCellValue('S' . $row, $data->work_start_date);  // Current Position
            $sheet->setCellValue('T' . $row, $data->reference_id);  // Working Since (or your desired field name)
            $sheet->setCellValue('U' . $row, $data->mobile_number);  // Reference ID
            $sheet->setCellValue('V' . $row, $data->email);  // Mobile Number
            $sheet->setCellValue('W' . $row, $data->temporary_address);  // Email ID
            $sheet->setCellValue('X' . $row, $data->permanent_address);  // Temporary Address
            $sheet->setCellValue('Y' . $row, $data->employee_cl);  // Permanent Address
            $sheet->setCellValue('Z' . $row, $data->employee_ol);  // CL Balance (or your desired field name)
            $sheet->setCellValue('AA' . $row, $data->employee_el);  // OL Balance (or your desired field name)
            $sheet->setCellValue('AB' . $row, $data->employee_ml);  // EL Balance (or your desired field name)
            $sheet->setCellValue('AB' . $row, $data->suspension_date);  // EL Balance (or your desired field name)
            $sheet->setCellValue('AD' . $row, $data->suspension_order_no);  // Suspension Date
            $sheet->setCellValue('AE' . $row, $data->suspension_remark);  // Suspension Order
            $sheet->setCellValue('AF' . $row, $data->bahal_date);  // Suspension Remark
            $sheet->setCellValue('AG' . $row, $data->bahal_order_no);  // Reinstate Date
            $sheet->setCellValue('AH' . $row, $data->bahal_remark);  // Reinstate Order
            $sheet->setCellValue('AI' . $row, $data->e_hrms_email);  // Reinstate Remark
            $sheet->setCellValue('AJ' . $row, $data->remarks);  // Remark

            $row++;
        }


        $writer = new Xlsx($spreadsheet);
        $filename = 'Employee_report.xlsx';
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        $writer->save('php://output');
        exit;
    }


    public function exportexcelJdEmployee()
    {
        $ulbWiseCounts = JdEmpRegistration::get();
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        $headers = [
            'A' => 'Sno.',
            'B' => 'वर्तमान JD का नाम',
            'C' => 'previous jd name',
            'D' => 'Registration no',
            'E' => 'अधिकारी/कर्मचारी का नाम',
            'F' => 'पिता का नाम',
            'G' => 'जन्मतिथि',
            'H' => 'पदनाम',
            'I' => 'वेतनमान',
            'J' => 'Gender',
            'K' => 'शैक्षणिक योग्यता',
            'L' => 'श्रेणी',
            'M' => 'कर्मचारी/अधिकारी की सेवा स्वरूप',
            'N' => 'निकाय सेवा में प्रथम प्रविष्टि का दिनांक',
            'O' => 'मूल निकाय का नाम',
            'P' => 'मूल विभाग',
            'Q' => 'कार्यरत अधिकारी/कर्मचारी का वर्तमान मूल पदनाम',
            'R' => 'वर्तमान निकाय में कब से कार्यरत हैं',
            'S' => 'रिफ्रेन्स आईडी(आधार कार्ड नंबर)',
            'T' => 'मोबाइल नंबर',
            'U' => 'ईमेल आईडी',
            'V' => 'अस्थाई पता',
            'W' => 'स्थाई पता',
            'X' => 'कुल शेष CL',
            'Y' => 'कुल शेष OL',
            'Z' => 'कुल शेष EL',
            'AA' => 'कुल शेष ML',
            'AB' => 'निलंबन तिथि',
            'AC' => 'निलंबन आदेश क्र',
            'AD' => 'Remark (निलंबन)',
            'AE' => 'बहाली तिथि',
            'AF' => 'बहाली आदेश क्र',
            'AG' => 'Remark (बहाली)',
            'AH' => 'e-HRMS मेल आईडी',
            'AI' => 'रिमार्क'
        ];

        foreach ($headers as $column => $header) {
            $sheet->setCellValue($column . '1', $header);
        }

        $row = 2;

        foreach ($ulbWiseCounts as $index => $data) {
            $sheet->setCellValue('A' . $row, $index + 1);
            $sheet->setCellValue('B' . $row, $data->jdname->name ?? '');
            $sheet->setCellValue('C' . $row, $data->jdname->name ?? '');
            $sheet->setCellValue('D' . $row, $data->registration_no ?? '');
            $sheet->setCellValue('E' . $row, $data->employee_name ?? '');
            $sheet->setCellValue('F' . $row, $data->father_name ?? '');
            $sheet->setCellValue('G' . $row, $data->birth_date ?? '');
            $sheet->setCellValue('H' . $row, $data->post->post_name ?? '');
            $sheet->setCellValue('I' . $row, $data->payscalename->payscale ?? '');
            $sheet->setCellValue('J' . $row, $data->gender ?? '');
            $sheet->setCellValue('K' . $row, $data->qualification ?? '');
            $sheet->setCellValue('L' . $row, $data->caste ?? '');
            $sheet->setCellValue('M' . $row, $data->employeeType->type ?? '');
            $sheet->setCellValue('N' . $row, $data->joining_date ?? '');
            $sheet->setCellValue('O' . $row, $data->ulbnew->name ?? '');
            $sheet->setCellValue('P' . $row, $data->department_name ?? '');
            $sheet->setCellValue('Q' . $row, $data->current_designation ?? '');
            $sheet->setCellValue('R' . $row, $data->work_start_date ?? '');
            $sheet->setCellValue('S' . $row, $data->reference_id ?? '');
            $sheet->setCellValue('T' . $row, $data->mobile_number ?? '');
            $sheet->setCellValue('U' . $row, $data->email ?? '');
            $sheet->setCellValue('V' . $row, $data->temporary_address ?? '');
            $sheet->setCellValue('W' . $row, $data->permanent_address ?? '');
            $sheet->setCellValue('X' . $row, $data->employee_cl ?? '');
            $sheet->setCellValue('Y' . $row, $data->employee_ol ?? '');
            $sheet->setCellValue('Z' . $row, $data->employee_el ?? '');
            $sheet->setCellValue('AA' . $row, $data->employee_ml ?? '');
            $sheet->setCellValue('AB' . $row, $data->suspension_date ?? '');
            $sheet->setCellValue('AC' . $row, $data->suspension_order_no ?? '');
            $sheet->setCellValue('AD' . $row, $data->suspension_remark ?? '');
            $sheet->setCellValue('AE' . $row, $data->bahal_date ?? '');
            $sheet->setCellValue('AF' . $row, $data->bahal_order_no ?? '');
            $sheet->setCellValue('AG' . $row, $data->bahal_remark ?? '');
            $sheet->setCellValue('AH' . $row, $data->e_hrms_email ?? '');
            $sheet->setCellValue('AI' . $row, $data->remarks ?? '');

            $row++;
        }

        $writer = new Xlsx($spreadsheet);
        $filename = 'JdEmployee_report.xlsx';
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        $writer->save('php://output');
        exit;
    }

    public function exportexcelLeaveDetail()
    {
        $ulbWiseCounts = LeaveApply::with(['ulb.division', 'post'])->get(); // Optimized with eager loading
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Updated Column Headers (Shifted left after G removal)
        $sheet->setCellValue('A1', 'Sno.');
        $sheet->setCellValue('B1', 'Leave Application no.');
        $sheet->setCellValue('C1', 'Division name');
        $sheet->setCellValue('D1', 'निकाय का नाम');
        $sheet->setCellValue('E1', 'अधिकारी का प्रकार');
        $sheet->setCellValue('F1', 'छुट्टी मे जाने वाले अधिकारी का नाम');
        $sheet->setCellValue('G1', 'छुट्टी apply की तारीख');
        $sheet->setCellValue('H1', 'छुट्टी का प्रकार');
        $sheet->setCellValue('I1', 'अवकाश अवधि किस तिथि से');
        $sheet->setCellValue('J1', 'अवकाश अवधि किस तिथि तक');
        $sheet->setCellValue('K1', 'स्वीकृतकर्ता का नाम');
        $sheet->setCellValue('L1', 'स्वीकृतकर्ता का पदनाम');
        $sheet->setCellValue('M1', 'चार्ज लेने वाले अधिकारी / कर्मचारी का नाम');
        $sheet->setCellValue('N1', 'चार्ज लेने वाले अधिकारी / कर्मचारी पदनाम');
        $sheet->setCellValue('O1', 'status');

        // Populating Data Rows (Shifted one column left)
        $row = 2;
        foreach ($ulbWiseCounts as $index => $data) {
            $sheet->setCellValue('A' . $row, $index + 1);
            $sheet->setCellValue('B' . $row, $data->leave_number);
            $sheet->setCellValue('C' . $row, $data->ulb->division->name);
            $sheet->setCellValue('D' . $row, $data->ulb->name);
            $sheet->setCellValue('E' . $row, $data->post->post_name);
            $sheet->setCellValue('F' . $row, $data->name_of_officer);
            $sheet->setCellValue('G' . $row, $data->date_inwhich_leave_applied);
            $sheet->setCellValue('H' . $row, $data->leave_type);
            $sheet->setCellValue('I' . $row, $data->leave_date_from);
            $sheet->setCellValue('J' . $row, $data->leave_date_to);
            $sheet->setCellValue('K' . $row, $data->name_of_officer_approvedleave);
            $sheet->setCellValue('L' . $row, $data->post_of_officer_approvedleave);
            $sheet->setCellValue('M' . $row, $data->charged_officer_name);
            $sheet->setCellValue('N' . $row, $data->charged_officer_post);
            $sheet->setCellValue('O' . $row, $data->status);
            $row++;
        }

        // File Output
        $writer = new Xlsx($spreadsheet);
        $filename = 'Leave_report.xlsx';

        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        $writer->save('php://output');
        exit;
    }



    public function deptexcelElectricityBill()
    {
        $ulbWiseCounts = ElectricityBill::get();
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        $sheet->setCellValue('A1', 'Sno.');
        $sheet->setCellValue('B1', 'Electricity bill no');
        $sheet->setCellValue('C1', 'Division name');
        $sheet->setCellValue('D1', 'Ulb name');
        $sheet->setCellValue('E1', 'Current Month Bill Amount');
        $sheet->setCellValue('F1', 'Previous Pending Bill Amount');
        $sheet->setCellValue('G1', 'Surcharge Amount');
        $sheet->setCellValue('H1', 'Total Amount');
        $sheet->setCellValue('I1', 'Previous Paid Bill Amount');
        $sheet->setCellValue('J1', 'Previous Paid Bill Date');
        $sheet->setCellValue('K1', 'Remark');



        $row = 2;
        foreach ($ulbWiseCounts as $index => $data) {
            $sheet->setCellValue('A' . $row, $index + 1);
            $sheet->setCellValue('B' . $row, $data->electricity_bill_no);
            $sheet->setCellValue('C' . $row, $data->ulb->division->name);
            $sheet->setCellValue('D' . $row, $data->ulb->name);
            $sheet->setCellValue('E' . $row, $data->current_month_bill_amount);
            $sheet->setCellValue('F' . $row, $data->pending_electricity_bill);
            $sheet->setCellValue('G' . $row, $data->surcharge_amount);
            $sheet->setCellValue('H' . $row, $data->total_amount);
            $sheet->setCellValue('I' . $row, $data->previous_paid_bill_amount);
            $sheet->setCellValue('J' . $row, $data->previous_paid_bill_date);
            $sheet->setCellValue('K' . $row, $data->remark);

            $row++;
        }

        $writer = new Xlsx($spreadsheet);
        $filename = 'electricity_bill.xlsx';
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        $writer->save('php://output');
        exit;
    }
    public function deptexportexcelNewMeter()
    {
        $ulbWiseCounts = NewMeterRegistration::get();
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        $sheet->setCellValue('A1', 'Sno.');
        $sheet->setCellValue('B1', 'Unique meter no');
        $sheet->setCellValue('C1', 'Division name');
        $sheet->setCellValue('D1', 'Ulb name');
        $sheet->setCellValue('E1', 'मीटर रजिस्ट्रेशन नंबर(बीपी नंबर)');
        $sheet->setCellValue('F1', 'मीटर किस नाम से रजिस्टर है');
        $sheet->setCellValue('G1', 'मीटर किस दिनांक से रजिस्टर है');
        $sheet->setCellValue('H1', 'मीटर लगे स्थान का Lattitude');
        $sheet->setCellValue('I1', 'मीटर लगे स्थान का Longitude');
        $sheet->setCellValue('J1', 'मीटर की स्थिति');
        $sheet->setCellValue('K1', 'Remark');



        $row = 2;
        foreach ($ulbWiseCounts as $index => $data) {
            $sheet->setCellValue('A' . $row, $index + 1);
            $sheet->setCellValue('B' . $row, $data->unique_meter_no);
            $sheet->setCellValue('C' . $row, $data->ulb->division->name);
            $sheet->setCellValue('D' . $row, $data->ulb->name);
            $sheet->setCellValue('E' . $row, $data->meter_registrtaion_no);
            $sheet->setCellValue('F' . $row, $data->meter_authorized_name);
            $sheet->setCellValue('G' . $row, $data->registration_date);
            $sheet->setCellValue('H' . $row, $data->latitude);
            $sheet->setCellValue('I' . $row, $data->longitude);
            $sheet->setCellValue('J' . $row, $data->meter_status);
            $sheet->setCellValue('K' . $row, $data->remark);
            $row++;
        }

        $writer = new Xlsx($spreadsheet);
        $filename = 'Newmeter_record.xlsx';
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        $writer->save('php://output');
        exit;
    }


    public function exportexcelchungimadh()
    {
        // Fetch and group the data from ChungiMadhVyay model
        $items = ChungiMadhVyay::with('ulb')
            ->get()
            ->groupBy('ulb_id')
            ->map(function ($ulbGroup) {
                return $ulbGroup->sortByDesc('updated_at')->first();
            });

        // Calculate total amount released for each ulb_id
        $totalAmountReleased = [];
        foreach ($items as $item) {
            $ulbId = $item->ulb_id;
            $totalAmountReleased[$ulbId] = ChungiMadhVyay::where('ulb_id', $ulbId)->sum('amount_released');
        }

        // Create a new Spreadsheet instance
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Set the header row
        $sheet->setCellValue('A1', 'Sno.');
        $sheet->setCellValue('B1', 'ULB Name');
        $sheet->setCellValue('C1', 'Total Amount Released');
        $sheet->setCellValue('D1', 'Expenditure Amount');
        $sheet->setCellValue('E1', 'Balance Amount');
        $sheet->setCellValue('F1', 'Remark');

        // Fill in the data rows
        $row = 2;  // Start from row 2 as row 1 is for headers
        foreach ($items as $index => $item) {
            $ulbId = $item->ulb_id;
            $sheet->setCellValue('A' . $row, $index + 1);  // Sno.
            $sheet->setCellValue('B' . $row, $item->ulb->name);
            $sheet->setCellValue('C' . $row, $totalAmountReleased[$ulbId] ?? 0);  // Total Amount Released
            $sheet->setCellValue('D' . $row, $item->expenditure_amount ?? 0);
            $sheet->setCellValue('E' . $row, $item->balance_amount ?? 0);
            $sheet->setCellValue('F' . $row, $item->remark ?? 'N/A');
            $row++;
        }

        // Create a writer for the spreadsheet
        $writer = new Xlsx($spreadsheet);

        // Set the filename for the downloaded file
        $filename = 'ChungiMadhVyay_Records.xlsx';

        // Set the headers to trigger a download
        return response()->stream(function () use ($writer) {
            $writer->save('php://output');
        }, 200, [
            'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'Content-Disposition' => 'attachment; filename="ChungiMadhVyay_Records.xlsx"',
            'Cache-Control' => 'max-age=0',
        ]);
    }
    public function exportexcelappatnidhi()
    {
        // Fetch and group the data from AppatnidhiMarammat model
        $appatnidhi = AppatnidhiMarammat::with('ulb')
            ->get()
            ->groupBy('ulb_id')
            ->map(function ($ulbGroup) {
                return $ulbGroup->sortByDesc('updated_at')->first();
            });

        // Calculate total amount released for each ulb_id
        $totalAmountReleasedtwo = [];
        foreach ($appatnidhi as $item) {
            $ulbId = $item->ulb_id;
            $totalAmountReleasedtwo[$ulbId] = AppatnidhiMarammat::where('ulb_id', $ulbId)->sum('amount_released');
        }

        // Create a new Spreadsheet instance
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Set the header row
        $sheet->setCellValue('A1', 'Sno.');
        $sheet->setCellValue('B1', 'ulb name');
        $sheet->setCellValue('C1', 'Total Amount Released');
        $sheet->setCellValue('D1', 'Expenditure Amount');
        $sheet->setCellValue('E1', 'Balance Amount');
        $sheet->setCellValue('F1', 'Remark');

        // Fill in the data rows
        $row = 2;  // Start from row 2 as row 1 is for headers
        foreach ($appatnidhi as $index => $item) {
            $ulbId = $item->ulb_id;
            $sheet->setCellValue('A' . $row, $index + 1);  // Sno.
            $sheet->setCellValue('B' . $row, $item->ulb->name);
            $sheet->setCellValue('C' . $row, $totalAmountReleasedtwo[$ulbId] ?? 0);  // Total Amount Released
            $sheet->setCellValue('D' . $row, $item->expenditure_amount ?? 0);
            $sheet->setCellValue('E' . $row, $item->balance_amount ?? 0);
            $sheet->setCellValue('F' . $row, $item->remark ?? 'N/A');
            $row++;
        }

        // Create a writer for the spreadsheet
        $writer = new Xlsx($spreadsheet);

        // Set the filename for the downloaded file
        $filename = 'AppatnidhiMarammat_Records.xlsx';

        // Set the headers to trigger a download
        return response()->stream(function () use ($writer) {
            $writer->save('php://output');
        }, 200, [
            'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            'Cache-Control' => 'max-age=0',
        ]);
    }

    public function exportexcelmahapaurnidhi()
    {
        // Fetch and group the data from MahapaurAdhyakshNidhi model
        $mahapaurnidhi = MahapaurAdhyakshNidhi::with('ulb')
            ->get()
            ->groupBy('ulb_id')
            ->map(function ($ulbGroup) {
                return $ulbGroup->sortByDesc('updated_at')->first();
            });

        // Calculate total amount released for each ulb_id
        $totalAmountReleasedthree = [];
        foreach ($mahapaurnidhi as $item) {
            $ulbId = $item->ulb_id;
            $totalAmountReleasedthree[$ulbId] = MahapaurAdhyakshNidhi::where('ulb_id', $ulbId)->sum('amount_released');
        }

        // Create a new Spreadsheet instance
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Set the header row
        $sheet->setCellValue('A1', 'Sno.');
        $sheet->setCellValue('B1', 'ulb name');
        $sheet->setCellValue('C1', 'Total Amount Released');
        $sheet->setCellValue('D1', 'Expenditure Amount');
        $sheet->setCellValue('E1', 'Balance Amount');
        $sheet->setCellValue('F1', 'Remark');

        // Fill in the data rows
        $row = 2;  // Start from row 2 as row 1 is for headers
        foreach ($mahapaurnidhi as $index => $item) {
            $ulbId = $item->ulb_id;
            $sheet->setCellValue('A' . $row, $index + 1);  // Sno.
            $sheet->setCellValue('B' . $row, $item->ulb->name);
            $sheet->setCellValue('C' . $row, $totalAmountReleasedthree[$ulbId] ?? 0);  // Total Amount Released
            $sheet->setCellValue('D' . $row, $item->expenditure_amount ?? 0);
            $sheet->setCellValue('E' . $row, $item->balance_amount ?? 0);
            $sheet->setCellValue('F' . $row, $item->remark ?? 'N/A');
            $row++;
        }

        // Create a writer for the spreadsheet
        $writer = new Xlsx($spreadsheet);

        // Set the filename for the downloaded file
        $filename = 'MahapaurAdhyakshNidhi_Records.xlsx';

        // Set the headers to trigger a download
        return response()->stream(function () use ($writer) {
            $writer->save('php://output');
        }, 200, [
            'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            'Cache-Control' => 'max-age=0',
        ]);
    }

    public function exportexcelparshadnidhi()
    {
        // Fetch and group the data from ParshadNidhi model
        $parshadnidhi = ParshadNidhi::with('ulb')
            ->get()
            ->groupBy('ulb_id')
            ->map(function ($ulbGroup) {
                return $ulbGroup->sortByDesc('updated_at')->first();
            });

        // Calculate total amount released for each ulb_id
        $totalAmountReleasedfour = [];
        foreach ($parshadnidhi as $item) {
            $ulbId = $item->ulb_id;
            $totalAmountReleasedfour[$ulbId] = ParshadNidhi::where('ulb_id', $ulbId)->sum('amount_released');
        }

        // Create a new Spreadsheet instance
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Set the header row
        $sheet->setCellValue('A1', 'Sno.');
        $sheet->setCellValue('B1', 'Ulb name');
        $sheet->setCellValue('C1', 'Total Amount Released');
        $sheet->setCellValue('D1', 'Expenditure Amount');
        $sheet->setCellValue('E1', 'Balance Amount');
        $sheet->setCellValue('F1', 'Remark');

        // Fill in the data rows
        $row = 2;  // Start from row 2 as row 1 is for headers
        foreach ($parshadnidhi as $index => $item) {
            $ulbId = $item->ulb_id;
            $sheet->setCellValue('A' . $row, $index + 1);  // Sno.
            $sheet->setCellValue('B' . $row, $item->ulb->name);
            $sheet->setCellValue('C' . $row, $totalAmountReleasedfour[$ulbId] ?? 0);  // Total Amount Released
            $sheet->setCellValue('D' . $row, $item->expenditure_amount ?? 0);
            $sheet->setCellValue('E' . $row, $item->balance_amount ?? 0);
            $sheet->setCellValue('F' . $row, $item->remark ?? 'N/A');
            $row++;
        }

        // Create a writer for the spreadsheet
        $writer = new Xlsx($spreadsheet);

        // Set the filename for the downloaded file
        $filename = 'ParshadNidhi_Records.xlsx';

        // Set the headers to trigger a download
        return response()->stream(function () use ($writer) {
            $writer->save('php://output');
        }, 200, [
            'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            'Cache-Control' => 'max-age=0',
        ]);
    }
    public function exportexcelswachcommando()
    {
        // Fetch and group the data from SwachCommando model
        $swachcommando = SwachCommando::with('ulb')
            ->get()
            ->groupBy('ulb_id')
            ->map(function ($ulbGroup) {
                return $ulbGroup->sortByDesc('updated_at')->first();
            });

        // Calculate total amount released for each ulb_id
        $totalAmountReleasedfive = [];
        foreach ($swachcommando as $item) {
            $ulbId = $item->ulb_id;
            $totalAmountReleasedfive[$ulbId] = SwachCommando::where('ulb_id', $ulbId)->sum('amount_released');
        }

        // Create a new Spreadsheet instance
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Set the header row
        $sheet->setCellValue('A1', 'Sno.');
        $sheet->setCellValue('B1', 'Ulb name');
        $sheet->setCellValue('C1', 'Total Amount Released');
        $sheet->setCellValue('D1', 'Expenditure Amount');
        $sheet->setCellValue('E1', 'Balance Amount');
        $sheet->setCellValue('F1', 'Remark');

        // Fill in the data rows
        $row = 2;  // Start from row 2 as row 1 is for headers
        foreach ($swachcommando as $index => $item) {
            $ulbId = $item->ulb_id;
            $sheet->setCellValue('A' . $row, $index + 1);  // Sno.
            $sheet->setCellValue('B' . $row, $item->ulb->name);
            $sheet->setCellValue('C' . $row, $totalAmountReleasedfive[$ulbId] ?? 0);  // Total Amount Released
            $sheet->setCellValue('D' . $row, $item->expenditure_amount ?? 0);
            $sheet->setCellValue('E' . $row, $item->balance_amount ?? 0);
            $sheet->setCellValue('F' . $row, $item->remark ?? 'N/A');
            $row++;
        }

        // Create a writer for the spreadsheet
        $writer = new Xlsx($spreadsheet);

        // Set the filename for the downloaded file
        $filename = 'SwachCommando_Records.xlsx';

        // Set the headers to trigger a download
        return response()->stream(function () use ($writer) {
            $writer->save('php://output');
        }, 200, [
            'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            'Cache-Control' => 'max-age=0',
        ]);
    }
    public function exportexcelcleancity()
    {
        // Fetch and group the data from MissionCleanCity model
        $cleancity = MissionCleanCity::with('ulb')
            ->get()
            ->groupBy('ulb_id')
            ->map(function ($ulbGroup) {
                return $ulbGroup->sortByDesc('updated_at')->first();
            });

        // Calculate total amount released for each ulb_id
        $totalAmountReleasedsix = [];
        foreach ($cleancity as $item) {
            $ulbId = $item->ulb_id;
            $totalAmountReleasedsix[$ulbId] = MissionCleanCity::where('ulb_id', $ulbId)->sum('amount_released');
        }

        // Create a new Spreadsheet instance
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Set the header row
        $sheet->setCellValue('A1', 'Sno.');
        $sheet->setCellValue('B1', 'Ulb name');
        $sheet->setCellValue('C1', 'Total Amount Released');
        $sheet->setCellValue('D1', 'Expenditure Amount');
        $sheet->setCellValue('E1', 'Balance Amount');
        $sheet->setCellValue('F1', 'Remark');

        // Fill in the data rows
        $row = 2;  // Start from row 2 as row 1 is for headers
        foreach ($cleancity as $index => $item) {
            $ulbId = $item->ulb_id;
            $sheet->setCellValue('A' . $row, $index + 1);  // Sno.
            $sheet->setCellValue('B' . $row, $item->ulb->name);
            $sheet->setCellValue('C' . $row, $totalAmountReleasedsix[$ulbId] ?? 0);  // Total Amount Released
            $sheet->setCellValue('D' . $row, $item->expenditure_amount ?? 0);
            $sheet->setCellValue('E' . $row, $item->balance_amount ?? 0);
            $sheet->setCellValue('F' . $row, $item->remark ?? 'N/A');
            $row++;
        }

        // Create a writer for the spreadsheet
        $writer = new Xlsx($spreadsheet);

        // Set the filename for the downloaded file
        $filename = 'MissionCleanCity_Records.xlsx';

        // Set the headers to trigger a download
        return response()->stream(function () use ($writer) {
            $writer->save('php://output');
        }, 200, [
            'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            'Cache-Control' => 'max-age=0',
        ]);
    }

    public function exportexcellambitvetan()
    {
        // Fetch and group the data from LambitVetan model
        $lambitvetan = LambitVetan::with('ulb')
            ->get()
            ->groupBy('ulb_id')
            ->map(function ($ulbGroup) {
                return $ulbGroup->sortByDesc('updated_at')->first();
            });

        // Calculate total amount released for each ulb_id
        $totalAmountReleasedseven = [];
        foreach ($lambitvetan as $item) {
            $ulbId = $item->ulb_id;
            $totalAmountReleasedseven[$ulbId] = LambitVetan::where('ulb_id', $ulbId)->sum('amount_released');
        }

        // Create a new Spreadsheet instance
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Set the header row
        $sheet->setCellValue('A1', 'Sno.');
        $sheet->setCellValue('B1', 'Ulb name');
        $sheet->setCellValue('C1', 'Total Amount Released');
        $sheet->setCellValue('D1', 'Expenditure Amount');
        $sheet->setCellValue('E1', 'Balance Amount');
        $sheet->setCellValue('F1', 'Remark');

        // Fill in the data rows
        $row = 2;  // Start from row 2 as row 1 is for headers
        foreach ($lambitvetan as $index => $item) {
            $ulbId = $item->ulb_id;
            $sheet->setCellValue('A' . $row, $index + 1);  // Sno.
            $sheet->setCellValue('B' . $row, $item->ulb->name);
            $sheet->setCellValue('C' . $row, $totalAmountReleasedseven[$ulbId] ?? 0);  // Total Amount Released
            $sheet->setCellValue('D' . $row, $item->expenditure_amount ?? 0);
            $sheet->setCellValue('E' . $row, $item->balance_amount ?? 0);
            $sheet->setCellValue('F' . $row, $item->remark ?? 'N/A');
            $row++;
        }

        // Create a writer for the spreadsheet
        $writer = new Xlsx($spreadsheet);

        // Set the filename for the downloaded file
        $filename = 'LambitVetan_Records.xlsx';

        // Set the headers to trigger a download
        return response()->stream(function () use ($writer) {
            $writer->save('php://output');
        }, 200, [
            'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            'Cache-Control' => 'max-age=0',
        ]);
    }
    public function exportexcelanyakarya()
    {
        // Fetch and group the data from AnyaKarya model
        $anyakarya = AnyaKarya::with('ulb')
            ->get()
            ->groupBy('ulb_id')
            ->map(function ($ulbGroup) {
                return $ulbGroup->sortByDesc('updated_at')->first();
            });

        // Calculate total amount released for each ulb_id
        $totalAmountReleasedeight = [];
        foreach ($anyakarya as $item) {
            $ulbId = $item->ulb_id;
            $totalAmountReleasedeight[$ulbId] = AnyaKarya::where('ulb_id', $ulbId)->sum('amount_released');
        }

        // Create a new Spreadsheet instance
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Set the header row
        $sheet->setCellValue('A1', 'Sno.');
        $sheet->setCellValue('B1', 'Ulb name');
        $sheet->setCellValue('C1', 'Total Amount Released');
        $sheet->setCellValue('D1', 'Expenditure Amount');
        $sheet->setCellValue('E1', 'Balance Amount');
        $sheet->setCellValue('F1', 'Remark');

        // Fill in the data rows
        $row = 2;  // Start from row 2 as row 1 is for headers
        foreach ($anyakarya as $index => $item) {
            $ulbId = $item->ulb_id;
            $sheet->setCellValue('A' . $row, $index + 1);  // Sno.
            $sheet->setCellValue('B' . $row, $item->ulb->name);
            $sheet->setCellValue('C' . $row, $totalAmountReleasedeight[$ulbId] ?? 0);  // Total Amount Released
            $sheet->setCellValue('D' . $row, $item->expenditure_amount ?? 0);
            $sheet->setCellValue('E' . $row, $item->balance_amount ?? 0);
            $sheet->setCellValue('F' . $row, $item->remark ?? 'N/A');
            $row++;
        }

        // Create a writer for the spreadsheet
        $writer = new Xlsx($spreadsheet);

        // Set the filename for the downloaded file
        $filename = 'AnyaKarya_Records.xlsx';

        // Set the headers to trigger a download
        return response()->stream(function () use ($writer) {
            $writer->save('php://output');
        }, 200, [
            'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            'Cache-Control' => 'max-age=0',
        ]);
    }
    public function exportexcelpeyjalnivaran()
    {
        // Fetch and group the data from PeyjalkastNivaran model
        $peyjalnivaran = PeyjalkastNivaran::with('ulb')
            ->get()
            ->groupBy('ulb_id')
            ->map(function ($ulbGroup) {
                return $ulbGroup->sortByDesc('updated_at')->first();
            });

        // Calculate total amount released for each ulb_id
        $totalAmountReleasednine = [];
        foreach ($peyjalnivaran as $item) {
            $ulbId = $item->ulb_id;
            $totalAmountReleasednine[$ulbId] = PeyjalkastNivaran::where('ulb_id', $ulbId)->sum('amount_released');
        }

        // Create a new Spreadsheet instance
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Set the header row
        $sheet->setCellValue('A1', 'Sno.');
        $sheet->setCellValue('B1', 'Ulb name');
        $sheet->setCellValue('C1', 'Total Amount Released');
        $sheet->setCellValue('D1', 'Expenditure Amount');
        $sheet->setCellValue('E1', 'Balance Amount');
        $sheet->setCellValue('F1', 'Remark');

        // Fill in the data rows
        $row = 2;  // Start from row 2 as row 1 is for headers
        foreach ($peyjalnivaran as $index => $item) {
            $ulbId = $item->ulb_id;
            $sheet->setCellValue('A' . $row, $index + 1);  // Sno.
            $sheet->setCellValue('B' . $row, $item->ulb->name);
            $sheet->setCellValue('C' . $row, $totalAmountReleasednine[$ulbId] ?? 0);  // Total Amount Released
            $sheet->setCellValue('D' . $row, $item->expenditure_amount ?? 0);
            $sheet->setCellValue('E' . $row, $item->balance_amount ?? 0);
            $sheet->setCellValue('F' . $row, $item->remark ?? 'N/A');
            $row++;
        }

        // Create a writer for the spreadsheet
        $writer = new Xlsx($spreadsheet);

        // Set the filename for the downloaded file
        $filename = 'PeyjalkastNivaran_Records.xlsx';

        // Set the headers to trigger a download
        return response()->stream(function () use ($writer) {
            $writer->save('php://output');
        }, 200, [
            'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            'Cache-Control' => 'max-age=0',
        ]);
    }
    public function exportexceljalpradaygirh()
    {
        // Fetch and group the data from JalPradayGirh model
        $jalpradaygirh = JalPradayGirh::with('ulb')
            ->get()
            ->groupBy('ulb_id')
            ->map(function ($ulbGroup) {
                return $ulbGroup->sortByDesc('updated_at')->first();
            });

        // Calculate total amount released for each ulb_id
        $totalAmountReleasedten = [];
        foreach ($jalpradaygirh as $item) {
            $ulbId = $item->ulb_id;
            $totalAmountReleasedten[$ulbId] = JalPradayGirh::where('ulb_id', $ulbId)->sum('amount_released');
        }

        // Create a new Spreadsheet instance
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Set the header row
        $sheet->setCellValue('A1', 'Sno.');
        $sheet->setCellValue('B1', 'Ulb name');
        $sheet->setCellValue('C1', 'Total Amount Released');
        $sheet->setCellValue('D1', 'Expenditure Amount');
        $sheet->setCellValue('E1', 'Balance Amount');
        $sheet->setCellValue('F1', 'Remark');

        // Fill in the data rows
        $row = 2;  // Start from row 2 as row 1 is for headers
        foreach ($jalpradaygirh as $index => $item) {
            $ulbId = $item->ulb_id;
            $sheet->setCellValue('A' . $row, $index + 1);  // Sno.
            $sheet->setCellValue('B' . $row, $item->ulb->name);
            $sheet->setCellValue('C' . $row, $totalAmountReleasedten[$ulbId] ?? 0);  // Total Amount Released
            $sheet->setCellValue('D' . $row, $item->expenditure_amount ?? 0);
            $sheet->setCellValue('E' . $row, $item->balance_amount ?? 0);
            $sheet->setCellValue('F' . $row, $item->remark ?? 'N/A');
            $row++;
        }

        // Create a writer for the spreadsheet
        $writer = new Xlsx($spreadsheet);

        // Set the filename for the downloaded file
        $filename = 'JalPradayGirh_Records.xlsx';

        // Set the headers to trigger a download
        return response()->stream(function () use ($writer) {
            $writer->save('php://output');
        }, 200, [
            'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            'Cache-Control' => 'max-age=0',
        ]);
    }

    public function exportexcelmudranksulak()
    {
        // Fetch and group the data from MudrankSulak model
        $mudranksulak = MudrankSulak::with('ulb')
            ->get()
            ->groupBy('ulb_id')
            ->map(function ($ulbGroup) {
                return $ulbGroup->sortByDesc('updated_at')->first();
            });

        // Calculate total amount released for each ulb_id
        $totalAmountReleasedeleven = [];
        foreach ($mudranksulak as $item) {
            $ulbId = $item->ulb_id;
            $totalAmountReleasedeleven[$ulbId] = MudrankSulak::where('ulb_id', $ulbId)->sum('amount_released');
        }

        // Create a new Spreadsheet instance
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Set the header row
        $sheet->setCellValue('A1', 'Sno.');
        $sheet->setCellValue('B1', 'Ulb name');
        $sheet->setCellValue('C1', 'Total Amount Released');
        $sheet->setCellValue('D1', 'Expenditure Amount');
        $sheet->setCellValue('E1', 'Balance Amount');
        $sheet->setCellValue('F1', 'Remark');

        // Fill in the data rows
        $row = 2;  // Start from row 2 as row 1 is for headers
        foreach ($mudranksulak as $index => $item) {
            $ulbId = $item->ulb_id;
            $sheet->setCellValue('A' . $row, $index + 1);  // Sno.
            $sheet->setCellValue('B' . $row, $item->ulb->name);
            $sheet->setCellValue('C' . $row, $totalAmountReleasedeleven[$ulbId] ?? 0);  // Total Amount Released
            $sheet->setCellValue('D' . $row, $item->expenditure_amount ?? 0);
            $sheet->setCellValue('E' . $row, $item->balance_amount ?? 0);
            $sheet->setCellValue('F' . $row, $item->remark ?? 'N/A');
            $row++;
        }

        // Create a writer for the spreadsheet
        $writer = new Xlsx($spreadsheet);

        // Set the filename for the downloaded file
        $filename = 'MudrankSulak_Records.xlsx';

        // Set the headers to trigger a download
        return response()->stream(function () use ($writer) {
            $writer->save('php://output');
        }, 200, [
            'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            'Cache-Control' => 'max-age=0',
        ]);
    }
    public function exportexcelbar()
    {
        // Fetch and group the data from Bar model
        $bar = Bar::with('ulb')
            ->get()
            ->groupBy('ulb_id')
            ->map(function ($ulbGroup) {
                return $ulbGroup->sortByDesc('updated_at')->first();
            });

        // Calculate total amount released for each ulb_id
        $totalAmountReleasedtwelve = [];
        foreach ($bar as $item) {
            $ulbId = $item->ulb_id;
            $totalAmountReleasedtwelve[$ulbId] = Bar::where('ulb_id', $ulbId)->sum('amount_released');
        }

        // Create a new Spreadsheet instance
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Set the header row
        $sheet->setCellValue('A1', 'Sno.');
        $sheet->setCellValue('B1', 'Ulb name');
        $sheet->setCellValue('C1', 'Total Amount Released');
        $sheet->setCellValue('D1', 'Expenditure Amount');
        $sheet->setCellValue('E1', 'Balance Amount');
        $sheet->setCellValue('F1', 'Remark');

        // Fill in the data rows
        $row = 2;  // Start from row 2 as row 1 is for headers
        foreach ($bar as $index => $item) {
            $ulbId = $item->ulb_id;
            $sheet->setCellValue('A' . $row, $index + 1);  // Sno.
            $sheet->setCellValue('B' . $row, $item->ulb->name);
            $sheet->setCellValue('C' . $row, $totalAmountReleasedtwelve[$ulbId] ?? 0);  // Total Amount Released
            $sheet->setCellValue('D' . $row, $item->expenditure_amount ?? 0);
            $sheet->setCellValue('E' . $row, $item->balance_amount ?? 0);
            $sheet->setCellValue('F' . $row, $item->remark ?? 'N/A');
            $row++;
        }

        // Create a writer for the spreadsheet
        $writer = new Xlsx($spreadsheet);

        // Set the filename for the downloaded file
        $filename = 'Bar_Records.xlsx';

        // Set the headers to trigger a download
        return response()->stream(function () use ($writer) {
            $writer->save('php://output');
        }, 200, [
            'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            'Cache-Control' => 'max-age=0',
        ]);
    }
    public function exportexcelyatrikar()
    {
        // Fetch and group the data from YatriKar model
        $yatrikar = YatriKar::with('ulb')
            ->get()
            ->groupBy('ulb_id')
            ->map(function ($ulbGroup) {
                return $ulbGroup->sortByDesc('updated_at')->first();
            });

        // Calculate total amount released for each ulb_id
        $totalAmountReleasedthirteen = [];
        foreach ($yatrikar as $item) {
            $ulbId = $item->ulb_id;
            $totalAmountReleasedthirteen[$ulbId] = YatriKar::where('ulb_id', $ulbId)->sum('amount_released');
        }

        // Create a new Spreadsheet instance
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Set the header row
        $sheet->setCellValue('A1', 'Sno.');
        $sheet->setCellValue('B1', 'Ulb name');
        $sheet->setCellValue('C1', 'Total Amount Released');
        $sheet->setCellValue('D1', 'Expenditure Amount');
        $sheet->setCellValue('E1', 'Balance Amount');
        $sheet->setCellValue('F1', 'Remark');

        // Fill in the data rows
        $row = 2;  // Start from row 2 as row 1 is for headers
        foreach ($yatrikar as $index => $item) {
            $ulbId = $item->ulb_id;
            $sheet->setCellValue('A' . $row, $index + 1);  // Sno.
            $sheet->setCellValue('B' . $row, $item->ulb->name);
            $sheet->setCellValue('C' . $row, $totalAmountReleasedthirteen[$ulbId] ?? 0);  // Total Amount Released
            $sheet->setCellValue('D' . $row, $item->expenditure_amount ?? 0);
            $sheet->setCellValue('E' . $row, $item->balance_amount ?? 0);
            $sheet->setCellValue('F' . $row, $item->remark ?? 'N/A');
            $row++;
        }

        // Create a writer for the spreadsheet
        $writer = new Xlsx($spreadsheet);

        // Set the filename for the downloaded file
        $filename = 'YatriKar_Records.xlsx';

        // Set the headers to trigger a download
        return response()->stream(function () use ($writer) {
            $writer->save('php://output');
        }, 200, [
            'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            'Cache-Control' => 'max-age=0',
        ]);
    }
    public function exportexcelutpadkar()
    {
        // Fetch and group the data from Utpadkar model
        $utpadkar = Utpadkar::with('ulb')
            ->get()
            ->groupBy('ulb_id')
            ->map(function ($ulbGroup) {
                return $ulbGroup->sortByDesc('updated_at')->first();
            });

        // Calculate total amount released for each ulb_id
        $totalAmountReleasedfourteen = [];
        foreach ($utpadkar as $item) {
            $ulbId = $item->ulb_id;
            $totalAmountReleasedfourteen[$ulbId] = Utpadkar::where('ulb_id', $ulbId)->sum('amount_released');
        }

        // Create a new Spreadsheet instance
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Set the header row
        $sheet->setCellValue('A1', 'Sno.');
        $sheet->setCellValue('B1', 'Ulb name');
        $sheet->setCellValue('C1', 'Total Amount Released');
        $sheet->setCellValue('D1', 'Expenditure Amount');
        $sheet->setCellValue('E1', 'Balance Amount');
        $sheet->setCellValue('F1', 'Remark');

        // Fill in the data rows
        $row = 2;  // Start from row 2 as row 1 is for headers
        foreach ($utpadkar as $index => $item) {
            $ulbId = $item->ulb_id;
            $sheet->setCellValue('A' . $row, $index + 1);  // Sno.
            $sheet->setCellValue('B' . $row, $item->ulb->name);
            $sheet->setCellValue('C' . $row, $totalAmountReleasedfourteen[$ulbId] ?? 0);  // Total Amount Released
            $sheet->setCellValue('D' . $row, $item->expenditure_amount ?? 0);
            $sheet->setCellValue('E' . $row, $item->balance_amount ?? 0);
            $sheet->setCellValue('F' . $row, $item->remark ?? 'N/A');
            $row++;
        }

        // Create a writer for the spreadsheet
        $writer = new Xlsx($spreadsheet);

        // Set the filename for the downloaded file
        $filename = 'Utpadkar_Records.xlsx';

        // Set the headers to trigger a download
        return response()->stream(function () use ($writer) {
            $writer->save('php://output');
        }, 200, [
            'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            'Cache-Control' => 'max-age=0',
        ]);
    }



    public function exportexceldeptRegularSalary()
    {
        $ulbWiseCounts = SalaryDetail::get();
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        $sheet->setCellValue('A1', 'Sno.');
        $sheet->setCellValue('B1', 'Salary unique no');
        $sheet->setCellValue('C1', 'Division name');
        $sheet->setCellValue('D1', 'निकाय का नाम');
        $sheet->setCellValue('E1', 'कुल नियमित कर्मचारी की संख्या');
        $sheet->setCellValue('F1', 'किस माह तक का वेतन भुगतान किया गया');
        $sheet->setCellValue('G1', 'किस दिनांक को भुगतान किया गया');
        $sheet->setCellValue('H1', 'कुल वेतन भुगतान राशि');
        $sheet->setCellValue('I1', 'कर्मचारी का अंशदान');
        $sheet->setCellValue('J1', 'नियोक्ता का अंशदान');
        $sheet->setCellValue('K1', 'Voucher No');

        $row = 2;
        foreach ($ulbWiseCounts as $index => $data) {
            $sheet->setCellValue('A' . $row, $index + 1);
            $sheet->setCellValue('B' . $row, $data->salary_number_regular);
            $sheet->setCellValue('C' . $row, $data->ulb->division->name);
            $sheet->setCellValue('D' . $row, $data->ulb->name);
            $sheet->setCellValue('E' . $row, $data->total_permanent_employee);
            $sheet->setCellValue('F' . $row, $data->month_upto_which_salary_paid);
            $sheet->setCellValue('G' . $row, $data->date_of_payement);
            $sheet->setCellValue('H' . $row, $data->total_amount_paid);
            $sheet->setCellValue('I' . $row, $data->employees_contribution);
            $sheet->setCellValue('J' . $row, $data->employer_contribution);
            $sheet->setCellValue('K' . $row, $data->voucher_no);

            $row++;
        }

        $writer = new Xlsx($spreadsheet);
        $filename = 'RegularSalary_Report.xlsx';
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        $writer->save('php://output');
        exit;
    }
    public function exportexceldeptPlacementSalary()
    {
        $ulbWiseCounts = SalaryDetailPlacement::get();
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        $sheet->setCellValue('A1', 'Sno.');
        $sheet->setCellValue('B1', 'Salary unique no');
        $sheet->setCellValue('C1', 'Division name');
        $sheet->setCellValue('D1', 'निकाय का नाम');
        $sheet->setCellValue('E1', 'कुल प्लेसमेंट कर्मचारी की संख्या');
        $sheet->setCellValue('F1', 'किस माह तक का वेतन भुगतान किया गया');
        $sheet->setCellValue('G1', 'किस दिनांक को भुगतान किया गया');
        $sheet->setCellValue('H1', 'कुल वेतन भुगतान राशि');
        $sheet->setCellValue('I1', 'Placement Agency Name');
        $sheet->setCellValue('J1', 'P.F Amount');
        $sheet->setCellValue('K1', 'ESIC Amount');

        $row = 2;
        foreach ($ulbWiseCounts as $index => $data) {
            $sheet->setCellValue('A' . $row, $index + 1);
            $sheet->setCellValue('B' . $row, $data->salary_number_placement);
            $sheet->setCellValue('C' . $row, $data->ulb->division->name);
            $sheet->setCellValue('D' . $row, $data->ulb->name);
            $sheet->setCellValue('E' . $row, $data->total_placement_employee);
            $sheet->setCellValue('F' . $row, $data->month_upto_which_salary_paid_placement);
            $sheet->setCellValue('G' . $row, $data->date_of_payement_placement);
            $sheet->setCellValue('H' . $row, $data->total_amount_paid_placement);
            $sheet->setCellValue('I' . $row, $data->placement_agency_name);
            $sheet->setCellValue('J' . $row, $data->pf_amount);
            $sheet->setCellValue('K' . $row, $data->esic_amount);

            $row++;
        }

        $writer = new Xlsx($spreadsheet);
        $filename = 'PlacementSalary_Report.xlsx';
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        $writer->save('php://output');
        exit;
    }
    public function exportexcelreinstateReport()
    {
        // Get only employees where suspension_order_no has a value
        $ulbWiseCounts = Employee::whereNotNull('bahal_order_no') // Ensure suspension_order_no is not null
            ->where('bahal_order_no', '!=', '') // Ensure suspension_order_no is not empty
            ->get();

        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Set the headers
        $sheet->setCellValue('A1', 'Sno.');
        $sheet->setCellValue('B1', 'Registration No');
        $sheet->setCellValue('C1', 'Division name');
        $sheet->setCellValue('D1', 'निकाय का नाम');
        $sheet->setCellValue('E1', 'अधिकारी/कर्मचारी का नाम');
        $sheet->setCellValue('F1', 'निलंबन तिथि');
        $sheet->setCellValue('G1', 'निलंबन आदेश क्र');
        $sheet->setCellValue('H1', 'निलंबन आदेश की प्रति');
        $sheet->setCellValue('I1', 'remark (निलंबन )');
        $sheet->setCellValue('J1', 'बहाली तिथि');
        $sheet->setCellValue('K1', 'बहाली आदेश क्र');
        $sheet->setCellValue('L1', 'बहाली आदेश की प्रति');
        $sheet->setCellValue('M1', 'remark (बहाली )');

        // Loop through filtered records and add to the sheet
        $row = 2;
        foreach ($ulbWiseCounts as $index => $data) {
            $sheet->setCellValue('A' . $row, $index + 1);
            $sheet->setCellValue('B' . $row, $data->registration_no);
            $sheet->setCellValue('C' . $row, $data->ulb->division->name);
            $sheet->setCellValue('D' . $row, $data->ulb->name);
            $sheet->setCellValue('E' . $row, $data->employee_name);
            $sheet->setCellValue('F' . $row, $data->suspension_date);
            $sheet->setCellValue('G' . $row, $data->suspension_order_no);
            $sheet->setCellValue('H' . $row, $data->suspension_letter);
            $sheet->setCellValue('I' . $row, $data->suspension_remark);
            $sheet->setCellValue('J' . $row, $data->bahal_date);
            $sheet->setCellValue('K' . $row, $data->bahal_order_no);
            $sheet->setCellValue('L' . $row, $data->bahal_letter);
            $sheet->setCellValue('M' . $row, $data->bahal_remark);

            $row++;
        }

        // Prepare and output the file
        $writer = new Xlsx($spreadsheet);
        $filename = 'Reinstate_Report.xlsx';
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        $writer->save('php://output');
        exit;
    }
    public function exportexcelsuspensionReport()
    {
        // Get only employees where suspension_order_no has a value
        $ulbWiseCounts = Employee::whereNotNull('suspension_date')
            ->whereNull('bahal_date')
            ->get();
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Set the headers
        $sheet->setCellValue('A1', 'Sno.');
        $sheet->setCellValue('B1', 'Registration No');
        $sheet->setCellValue('C1', 'Division name');
        $sheet->setCellValue('D1', 'निकाय का नाम');
        $sheet->setCellValue('E1', 'अधिकारी/कर्मचारी का नाम');
        $sheet->setCellValue('F1', 'निलंबन तिथि');
        $sheet->setCellValue('G1', 'निलंबन आदेश क्र');
        $sheet->setCellValue('H1', 'निलंबन आदेश की प्रति');
        $sheet->setCellValue('I1', 'remark (निलंबन )');

        // Loop through filtered records and add to the sheet
        $row = 2;
        foreach ($ulbWiseCounts as $index => $data) {
            $sheet->setCellValue('A' . $row, $index + 1);
            $sheet->setCellValue('B' . $row, $data->registration_no);
            $sheet->setCellValue('C' . $row, $data->ulb->division->name);
            $sheet->setCellValue('D' . $row, $data->ulb->name);
            $sheet->setCellValue('E' . $row, $data->employee_name);
            $sheet->setCellValue('F' . $row, $data->suspension_date);
            $sheet->setCellValue('G' . $row, $data->suspension_order_no);
            $sheet->setCellValue('H' . $row, $data->suspension_letter);
            $sheet->setCellValue('I' . $row, $data->suspension_remark);

            $row++;
        }

        // Prepare and output the file
        $writer = new Xlsx($spreadsheet);
        $filename = 'Suspension_Report.xlsx';
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        $writer->save('php://output');
        exit;
    }


    public function addmasterdata()
    {
        $emptypes = EmployeeType::get();
        return view('departments.adddetail', compact('emptypes'));
    }
    public function addemptypedata()
    {
        $emptypes = EmployeeType::get();
        return view('departments.addemptype', compact('emptypes'));
    }
    public function addemptypedatasubmit(Request $request)
    {
        $request->validate([
            'type' => 'required',

        ]);

        $data = new EmployeeType();
        $data->type = $request->input('type');
        $data->save();
        Alert::success('Success!', 'submitted successfully');
        return back();
    }
    public function addemptypeupdate(Request $request, $id)
    {
        $emptypes = EmployeeType::find($id);
        $request->validate([
            'type' => 'required',

        ]);

        $emptypes->type = $request->input('type');
        $emptypes->save();
        Alert::success('Success!', 'Updated successfully');
        return back();
    }

    public function addemppostdata()
    {
        $postname = EmployeePost::get();
        return view('departments.addemppostdata', compact('postname'));
    }
    public function addemppostdatasubmit(Request $request)
    {
        $request->validate([
            'post_name' => 'required',

        ]);

        $data = new EmployeePost();
        $data->post_name = $request->input('post_name');
        $data->save();
        Alert::success('Success!', 'submitted successfully');
        return back();
    }

    public function addemppostupdate(Request $request, $id)
    {
        $postname = EmployeePost::find($id);
        $request->validate([
            'post_name' => 'required',

        ]);

        $postname->post_name = $request->input('post_name');
        $postname->save();
        Alert::success('Success!', 'Updated successfully');
        return back();
    }
    public function addemppayscaledata()
    {
        $emppayscale = Payscale::get();
        return view('departments.addemppayscaledata', compact('emppayscale'));
    }
    public function addemppayscaledatasubmit(Request $request)
    {
        $request->validate([
            'payscale' => 'required',

        ]);

        $data = new Payscale();
        $data->payscale = $request->input('payscale');
        $data->save();
        Alert::success('Success!', 'submitted successfully');
        return back();
    }
    public function addpayscaleupdate(Request $request, $id)
    {
        $emppayscale = Payscale::find($id);
        $request->validate([
            'payscale' => 'required',

        ]);

        $emppayscale->payscale = $request->input('payscale');
        $emppayscale->save();
        Alert::success('Success!', 'Updated successfully');
        return back();
    }
    public function adddistrictdata()
    {
        $division = MasterDivision::get();
        $district = MasterDistrict::get();
        return view('departments.adddistrictdata', compact('district', 'division'));
    }
    public function adddistrictdatasubmit(Request $request)
    {
        $request->validate([
            'name' => 'required',
            'eng_name' => 'required',
            'division_id' => 'required'
        ]);

        $data = new MasterDistrict();
        $data->name = $request->input('name');
        $data->eng_name = $request->input('eng_name');
        $data->division_id = $request->input('division_id');
        $data->save();
        Alert::success('Success!', 'submitted successfully');
        return back();
    }
    public function adddistrictupdate(Request $request, $id)
    {
        $district = MasterDistrict::find($id);
        $request->validate([
            'name' => 'required',
            'eng_name' => 'required',
            'division_id' => 'required'
        ]);

        $district->name = $request->input('name');
        $district->eng_name = $request->input('eng_name');
        $district->division_id = $request->input('division_id');
        $district->save();
        Alert::success('Success!', 'Updated successfully');
        return back();
    }

    public function deptotherinformation()
    {
        $muktidhamcount = Muktidham::count();
        $talabcount = TalabInformation::count();
        $gardencount = GardenInfo::count();
        $complexcount = Complexinformation::count();
        $streetlightcount = StreetlightInfo::count();
        $playgroundcount = PlaygroundInfo::count();
        $handpumpcount = HandpumInfo::count();
        $panitankicount = PanitankiInfo::count();
        $peyjalcount = PeyjalInfo::count();
        $jaljanitcount = jaljanitbimari::count();
        $trafficlightcount = TrafficLight::count();
        $govtbuildingcount = GovtBuilding::count();
        $deptrainwaterharvestingcount = RainWaterHarvesting::count();
        $deptslrmcount = SlrmCenterBhawan::count();
        $deptgouthancount = GouthanGovdhamKanjiHouse::count();
        $deptstpcount = Stp::count();
        $deptwtpcount = Wtp::count();
        $deptgymcount = Gym::count();
        $depttoiletcount = Toilet::count();

        return view('departments.deptotherinformation', compact('muktidhamcount', 'talabcount', 'gardencount', 'complexcount', 'streetlightcount', 'playgroundcount', 'handpumpcount', 'panitankicount', 'peyjalcount', 'jaljanitcount', 'trafficlightcount', 'govtbuildingcount', 'deptrainwaterharvestingcount', 'deptslrmcount', 'deptgouthancount', 'deptstpcount', 'deptwtpcount', 'deptgymcount', 'depttoiletcount'));
    }
    public function deptmuktidham()
    {
        $items = Muktidham::get();
        $nikays = User::all();
        return view('departments.deptmuktidham', compact('items', 'nikays'));
    }

    public function depttalabinformation()
    {
        $items = TalabInformation::get();
        $nikays = User::all();
        return view('departments.depttalabinformation', compact('items', 'nikays'));
    }
    public function deptgardeninformation()
    {
        $items = GardenInfo::get();
        $nikays = User::all();
        return view('departments.deptgardeninformation', compact('items', 'nikays'));
    }
    public function deptcomplexinformation()
    {
        $items = Complexinformation::get();
        $nikays = User::all();
        return view('departments.deptcomplexinformation', compact('items', 'nikays'));
    }
    public function deptstreetlightinformation()
    {
        $items = StreetlightInfo::get();
        $nikays = User::all();
        return view('departments.deptstreetlightinformation', compact('items', 'nikays'));
    }
    public function deptplaygroundinformation()
    {
        $items = PlaygroundInfo::get();
        $nikays = User::all();
        return view('departments.deptplaygroundinformation', compact('items', 'nikays'));
    }
    public function depthandpumpinformation()
    {
        $items = HandpumInfo::get();
        $nikays = User::all();
        return view('departments.depthandpumpinformation', compact('items', 'nikays'));
    }
    public function deptpanitankiinformation()
    {
        $items = PanitankiInfo::get();
        $nikays = User::all();
        return view('departments.deptpanitankiinformation', compact('items', 'nikays'));
    }
    public function deptpayjalinformation()
    {
        $items = PeyjalInfo::get();
        $nikays = User::all();
        return view('departments.deptpayjalinformation', compact('items', 'nikays'));
    }
    public function deptjaljanitbimari()
    {
        $items = jaljanitbimari::get();
        $nikays = User::all();
        return view('departments.deptjaljanitbimari', compact('items', 'nikays'));
    }
    public function depttrafficlight()
    {
        $item = TrafficLight::get();
        $nikays = User::all();
        return view('departments.depttrafficlight', compact('item', 'nikays'));
    }
    public function deptgovtbuilding()
    {
        $item = GovtBuilding::get();
        $nikays = User::all();
        return view('departments.deptgovtbuilding', compact('item', 'nikays'));
    }
    public function deptrainwaterharvesting()
    {
        $item = RainWaterHarvesting::get();
        $nikays = User::all();
        return view('departments.deptrainwaterharvesting', compact('item', 'nikays'));
    }
    public function deptslrmbhawans()
    {
        $items = SlrmCenterBhawan::get();
        $nikays = User::all();
        return view('departments.deptslrmbhawans', compact('items', 'nikays'));
    }
    public function deptgouthangovdhamkanjihouse()
    {
        $items = GouthanGovdhamKanjiHouse::get();
        $nikays = User::all();
        return view('departments.deptgouthangovdhamkanjihouse', compact('items', 'nikays'));
    }
    public function deptstpinfo()
    {
        $items = Stp::get();
        $nikays = User::all();
        return view('departments.deptstpinfo', compact('items', 'nikays'));
    }
    public function deptwtpinfo()
    {
        $items = Wtp::get();
        $nikays = User::all();
        return view('departments.deptwtpinfo', compact('items', 'nikays'));
    }
    public function deptgyminfo()
    {
        $items = Gym::get();
        $nikays = User::all();
        return view('departments.deptgyminfo', compact('items', 'nikays'));
    }
    public function depttoiletinfo()
    {
        $items = Toilet::get();
        $nikays = User::all();
        return view('departments.depttoiletinfo', compact('items', 'nikays'));
    }

    public function exportexcelmuktidham()
    {
        $items = Muktidham::get();
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        $sheet->setCellValue('A1', 'क़');
        $sheet->setCellValue('B1', 'यूनिक नंबर');
        $sheet->setCellValue('C1', 'जिला का नाम');
        $sheet->setCellValue('D1', 'निकाय का नाम ');
        $sheet->setCellValue('E1', 'वित्तीय वर्ष');
        $sheet->setCellValue('F1', 'दिनांक');
        $sheet->setCellValue('G1', 'वार्ड क्रमांक');
        $sheet->setCellValue('H1', 'वार्ड का नाम');
        $sheet->setCellValue('I1', 'स्थान का नाम');
        $sheet->setCellValue('J1', 'latitude');
        $sheet->setCellValue('K1', 'longitude');

        $row = 2;
        foreach ($items as $index => $data) {
            $sheet->setCellValue('A' . $row, $index + 1);
            $sheet->setCellValue('B' . $row, $data->unique_id ?? 'N/A');
            $sheet->setCellValue('C' . $row, $data->ulb->district->name ?? 'N/A');
            $sheet->setCellValue('D' . $row, $data->ulb->name ?? 'N/A');
            $sheet->setCellValue('E' . $row, $data->financial_year ?? 'N/A');
            $sheet->setCellValue('F' . $row, $data->dateInput ?? 'N/A');
            $sheet->setCellValue('G' . $row, $data->ward_no ?? 'N/A');
            $sheet->setCellValue('H' . $row, $data->ward_name ?? 'N/A');
            $sheet->setCellValue('I' . $row, $data->muktidham_address ?? 'N/A');
            $sheet->setCellValue('J' . $row, $data->latitude ?? 'N/A');
            $sheet->setCellValue('K' . $row, $data->longitude ?? 'N/A');

            $row++;
        }

        $writer = new Xlsx($spreadsheet);
        $filename = 'Muktidham_Records.xlsx';
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        $writer->save('php://output');
        exit;
    }
    public function exportExcelTalabInformation()
    {
        $items = TalabInformation::get();
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        $sheet->setCellValue('A1', 'क़');
        $sheet->setCellValue('B1', 'यूनिक नंबर');
        $sheet->setCellValue('C1', 'जिला का नाम');
        $sheet->setCellValue('D1', 'निकाय का नाम ');
        $sheet->setCellValue('E1', 'वित्तीय वर्ष');
        $sheet->setCellValue('F1', 'दिनांक');
        $sheet->setCellValue('G1', 'वार्ड क्रमांक');
        $sheet->setCellValue('H1', 'वार्ड का नाम');
        $sheet->setCellValue('I1', 'स्थान का नाम');
        $sheet->setCellValue('J1', 'latitude');
        $sheet->setCellValue('K1', 'longitude');
        $sheet->setCellValue('L1', 'क्षेत्रफल');

        $row = 2;
        foreach ($items as $index => $data) {
            $sheet->setCellValue('A' . $row, $index + 1);
            $sheet->setCellValue('B' . $row, $data->unique_id ?? 'N/A');
            $sheet->setCellValue('C' . $row, $data->ulb->district->name ?? 'N/A');
            $sheet->setCellValue('D' . $row, $data->ulb->name ?? 'N/A');
            $sheet->setCellValue('E' . $row, $data->financial_year ?? 'N/A');
            $sheet->setCellValue('F' . $row, $data->dateInput ?? 'N/A');
            $sheet->setCellValue('G' . $row, $data->ward_no ?? 'N/A');
            $sheet->setCellValue('H' . $row, $data->ward_name ?? 'N/A');
            $sheet->setCellValue('I' . $row, $data->talab_address ?? 'N/A');
            $sheet->setCellValue('J' . $row, $data->latitude ?? 'N/A');
            $sheet->setCellValue('K' . $row, $data->longitude ?? 'N/A');
            $sheet->setCellValue('L' . $row, $data->area ?? 'N/A');

            $row++;
        }

        $writer = new Xlsx($spreadsheet);
        $filename = 'talab_Records.xlsx';
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        $writer->save('php://output');
        exit;
    }
    public function exportExcelGardenInformation()
    {
        $items = GardenInfo::get();
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        $sheet->setCellValue('A1', 'क़');
        $sheet->setCellValue('B1', 'यूनिक नंबर');
        $sheet->setCellValue('C1', 'जिला का नाम');
        $sheet->setCellValue('D1', 'निकाय का नाम ');
        $sheet->setCellValue('E1', 'वित्तीय वर्ष');
        $sheet->setCellValue('F1', 'दिनांक');
        $sheet->setCellValue('G1', 'वार्ड क्रमांक');
        $sheet->setCellValue('H1', 'वार्ड का नाम');
        $sheet->setCellValue('I1', 'स्थान का नाम');
        $sheet->setCellValue('J1', 'latitude');
        $sheet->setCellValue('K1', 'longitude');
        $sheet->setCellValue('L1', 'क्षेत्रफल');

        $row = 2;
        foreach ($items as $index => $data) {
            $sheet->setCellValue('A' . $row, $index + 1);
            $sheet->setCellValue('B' . $row, $data->unique_id ?? 'N/A');
            $sheet->setCellValue('C' . $row, $data->ulb->district->name ?? 'N/A');
            $sheet->setCellValue('D' . $row, $data->ulb->name ?? 'N/A');
            $sheet->setCellValue('E' . $row, $data->financial_year ?? 'N/A');
            $sheet->setCellValue('F' . $row, $data->dateInput ?? 'N/A');
            $sheet->setCellValue('G' . $row, $data->ward_no ?? 'N/A');
            $sheet->setCellValue('H' . $row, $data->ward_name ?? 'N/A');
            $sheet->setCellValue('I' . $row, $data->garden_address ?? 'N/A');
            $sheet->setCellValue('J' . $row, $data->latitude ?? 'N/A');
            $sheet->setCellValue('K' . $row, $data->longitude ?? 'N/A');
            $sheet->setCellValue('L' . $row, $data->area ?? 'N/A');

            $row++;
        }

        $writer = new Xlsx($spreadsheet);
        $filename = 'Garden_Records.xlsx';
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        $writer->save('php://output');
        exit;
    }
    public function exportExcelComplexInformation()
    {
        $items = Complexinformation::get();
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        $sheet->setCellValue('A1', 'क़');
        $sheet->setCellValue('B1', 'यूनिक नंबर');
        $sheet->setCellValue('C1', 'जिला का नाम');
        $sheet->setCellValue('D1', 'निकाय का नाम ');
        $sheet->setCellValue('E1', 'वित्तीय वर्ष');
        $sheet->setCellValue('F1', 'दिनांक');
        $sheet->setCellValue('G1', 'वार्ड क्रमांक');
        $sheet->setCellValue('H1', 'वार्ड का नाम');
        $sheet->setCellValue('I1', 'स्थान का नाम');
        $sheet->setCellValue('I1', 'काम्प्लेक्स स्थिति');
        $sheet->setCellValue('J1', 'latitude');
        $sheet->setCellValue('K1', 'longitude');
        $sheet->setCellValue('L1', 'क्षेत्रफल');

        $row = 2;
        foreach ($items as $index => $data) {
            $sheet->setCellValue('A' . $row, $index + 1);
            $sheet->setCellValue('B' . $row, $data->unique_id ?? 'N/A');
            $sheet->setCellValue('C' . $row, $data->ulb->district->name ?? 'N/A');
            $sheet->setCellValue('D' . $row, $data->ulb->name ?? 'N/A');
            $sheet->setCellValue('E' . $row, $data->financial_year ?? 'N/A');
            $sheet->setCellValue('F' . $row, $data->dateInput ?? 'N/A');
            $sheet->setCellValue('G' . $row, $data->ward_no ?? 'N/A');
            $sheet->setCellValue('H' . $row, $data->ward_name ?? 'N/A');
            $sheet->setCellValue('I' . $row, $data->complex_address ?? 'N/A');
            $sheet->setCellValue('I' . $row, $data->complex_conditon ?? 'N/A');
            $sheet->setCellValue('J' . $row, $data->latitude ?? 'N/A');
            $sheet->setCellValue('K' . $row, $data->longitude ?? 'N/A');
            $sheet->setCellValue('L' . $row, $data->area ?? 'N/A');

            $row++;
        }

        $writer = new Xlsx($spreadsheet);
        $filename = 'complex_Records.xlsx';
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        $writer->save('php://output');
        exit;
    }
    public function exportExcelStreetlightInformation()
    {
        $items = StreetlightInfo::get();
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        $sheet->setCellValue('A1', 'क़.');
        $sheet->setCellValue('B1', 'यूनिक नंबर');
        $sheet->setCellValue('C1', 'जिला का नाम');
        $sheet->setCellValue('D1', 'निकाय का नाम ');
        $sheet->setCellValue('E1', 'वित्तीय वर्ष');
        $sheet->setCellValue('F1', 'दिनांक');
        $sheet->setCellValue('G1', 'कुल पोल की संख्या');
        $sheet->setCellValue('H1', 'लाइट नहीं लगे पोल की संख्या');
        $sheet->setCellValue('I1', 'कुल लगे लाइट की संख्या');
        $sheet->setCellValue('J1', 'चालू लाइट की संख्या');
        $sheet->setCellValue('J1', 'बंद लाइट की संख्या');

        $row = 2;
        foreach ($items as $index => $data) {
            $sheet->setCellValue('A' . $row, $index + 1);
            $sheet->setCellValue('B' . $row, $data->unique_id ?? 'N/A');
            $sheet->setCellValue('C' . $row, $data->ulb->district->name ?? 'N/A');
            $sheet->setCellValue('D' . $row, $data->ulb->name ?? 'N/A');
            $sheet->setCellValue('E' . $row, $data->financial_year ?? 'N/A');
            $sheet->setCellValue('F' . $row, $data->dateInput ?? 'N/A');
            $sheet->setCellValue('G' . $row, $data->total_pole ?? 'N/A');
            $sheet->setCellValue('H' . $row, $data->pole_without_light ?? 'N/A');
            $sheet->setCellValue('I' . $row, $data->total_light ?? 'N/A');
            $sheet->setCellValue('J' . $row, $data->total_working_light ?? 'N/A');
            $sheet->setCellValue('K' . $row, $data->total_not_working_light ?? 'N/A');


            $row++;
        }

        $writer = new Xlsx($spreadsheet);
        $filename = 'street_light.xlsx';
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        $writer->save('php://output');
        exit;
    }
    public function exportExcelPlaygroundInformation()
    {
        $items = PlaygroundInfo::get();
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        $sheet->setCellValue('A1', 'क़');
        $sheet->setCellValue('B1', 'यूनिक नंबर');
        $sheet->setCellValue('C1', 'जिला का नाम');
        $sheet->setCellValue('D1', 'निकाय का नाम ');
        $sheet->setCellValue('E1', 'वित्तीय वर्ष');
        $sheet->setCellValue('F1', 'दिनांक');
        $sheet->setCellValue('G1', 'वार्ड क्रमांक');
        $sheet->setCellValue('H1', 'वार्ड का नाम');
        $sheet->setCellValue('I1', 'स्थान का नाम');
        $sheet->setCellValue('J1', 'latitude');
        $sheet->setCellValue('K1', 'longitude');
        $sheet->setCellValue('L1', 'क्षेत्रफल (वर्ग मीटर)');

        $row = 2;
        foreach ($items as $index => $data) {
            $sheet->setCellValue('A' . $row, $index + 1);
            $sheet->setCellValue('B' . $row, $data->unique_id ?? 'N/A');
            $sheet->setCellValue('C' . $row, $data->ulb->district->name ?? 'N/A');
            $sheet->setCellValue('D' . $row, $data->ulb->name ?? 'N/A');
            $sheet->setCellValue('E' . $row, $data->financial_year ?? 'N/A');
            $sheet->setCellValue('F' . $row, $data->dateInput ?? 'N/A');
            $sheet->setCellValue('G' . $row, $data->ward_no ?? 'N/A');
            $sheet->setCellValue('H' . $row, $data->ward_name ?? 'N/A');
            $sheet->setCellValue('I' . $row, $data->playground_address ?? 'N/A');
            $sheet->setCellValue('J' . $row, $data->latitude ?? 'N/A');
            $sheet->setCellValue('K' . $row, $data->longitude ?? 'N/A');
            $sheet->setCellValue('L' . $row, $data->area ?? 'N/A');

            $row++;
        }

        $writer = new Xlsx($spreadsheet);
        $filename = 'playground_Records.xlsx';
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        $writer->save('php://output');
        exit;
    }
    public function exportExcelHandpumpInformation()
    {
        $items = HandpumInfo::get();
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        $sheet->setCellValue('A1', 'क़');
        $sheet->setCellValue('B1', 'यूनिक नंबर');
        $sheet->setCellValue('C1', 'जिला का नाम');
        $sheet->setCellValue('D1', 'निकाय का नाम ');
        $sheet->setCellValue('E1', 'वित्तीय वर्ष');
        $sheet->setCellValue('F1', 'दिनांक');
        $sheet->setCellValue('G1', 'कुल हैण्डपंप');
        $sheet->setCellValue('H1', 'हैण्डपंप चालू');
        $sheet->setCellValue('I1', 'हैण्डपंप बंद');
        $sheet->setCellValue('J1', 'कुल बोरेवेल');
        $sheet->setCellValue('K1', 'बोरेवेल चालू');
        $sheet->setCellValue('L1', 'बोरेवेल बंद');

        $row = 2;
        foreach ($items as $index => $data) {
            $sheet->setCellValue('A' . $row, $index + 1);
            $sheet->setCellValue('B' . $row, $data->unique_id ?? 'N/A');
            $sheet->setCellValue('C' . $row, $data->ulb->district->name ?? 'N/A');
            $sheet->setCellValue('D' . $row, $data->ulb->name ?? 'N/A');
            $sheet->setCellValue('E' . $row, $data->financial_year ?? 'N/A');
            $sheet->setCellValue('F' . $row, $data->dateInput ?? 'N/A');
            $sheet->setCellValue('G' . $row, $data->total_handpump ?? 'N/A');
            $sheet->setCellValue('H' . $row, $data->handpump_working ?? 'N/A');
            $sheet->setCellValue('I' . $row, $data->handpump_not_working ?? 'N/A');
            $sheet->setCellValue('J' . $row, $data->total_borewell ?? 'N/A');
            $sheet->setCellValue('K' . $row, $data->borewell_working ?? 'N/A');
            $sheet->setCellValue('L' . $row, $data->borewell_not_working ?? 'N/A');

            $row++;
        }

        $writer = new Xlsx($spreadsheet);
        $filename = 'handpump_borewells_Records.xlsx';
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        $writer->save('php://output');
        exit;
    }
    public function exportExcelPanitankiInformation()
    {
        $items = PanitankiInfo::get();
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        $sheet->setCellValue('A1', 'क़');
        $sheet->setCellValue('B1', 'यूनिक नंबर');
        $sheet->setCellValue('C1', 'जिला का नाम');
        $sheet->setCellValue('D1', 'निकाय का नाम ');
        $sheet->setCellValue('E1', 'वित्तीय वर्ष');
        $sheet->setCellValue('F1', 'दिनांक');
        $sheet->setCellValue('G1', 'वार्ड क्रमांक');
        $sheet->setCellValue('H1', 'वार्ड का नाम');
        $sheet->setCellValue('I1', 'स्थान का नाम');
        $sheet->setCellValue('J1', 'latitude');
        $sheet->setCellValue('K1', 'longitude');
        $sheet->setCellValue('L1', 'स्टोरेज कैपिसिटी(MLD में)');

        $row = 2;
        foreach ($items as $index => $data) {
            $sheet->setCellValue('A' . $row, $index + 1);
            $sheet->setCellValue('B' . $row, $data->unique_id ?? 'N/A');
            $sheet->setCellValue('C' . $row, $data->ulb->district->name ?? 'N/A');
            $sheet->setCellValue('D' . $row, $data->ulb->name ?? 'N/A');
            $sheet->setCellValue('E' . $row, $data->financial_year ?? 'N/A');
            $sheet->setCellValue('F' . $row, $data->dateInput ?? 'N/A');
            $sheet->setCellValue('G' . $row, $data->ward_no ?? 'N/A');
            $sheet->setCellValue('H' . $row, $data->ward_name ?? 'N/A');
            $sheet->setCellValue('I' . $row, $data->panitaki_address ?? 'N/A');
            $sheet->setCellValue('J' . $row, $data->latitude ?? 'N/A');
            $sheet->setCellValue('K' . $row, $data->longitude ?? 'N/A');
            $sheet->setCellValue('L' . $row, $data->panitaki_capacity ?? 'N/A');

            $row++;
        }

        $writer = new Xlsx($spreadsheet);
        $filename = 'Panitanki_Records.xlsx';
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        $writer->save('php://output');
        exit;
    }
    public function exportExcelPayjalInformation()
    {
        $items = PeyjalInfo::get();
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        $sheet->setCellValue('A1', 'क़');
        $sheet->setCellValue('B1', 'यूनिक नंबर');
        $sheet->setCellValue('C1', 'जिला का नाम');
        $sheet->setCellValue('D1', 'निकाय का नाम ');
        $sheet->setCellValue('E1', 'वित्तीय वर्ष');
        $sheet->setCellValue('F1', 'दिनांक');
        $sheet->setCellValue('G1', 'योजना के संचालन का माध्यम');
        $sheet->setCellValue('H1', 'PHE से हैंडओवर की तिथि');
        $sheet->setCellValue('I1', 'PHE से हैंडओवर नहीं लेने का कारण');
        $sheet->setCellValue('J1', 'WTP की संख्या');
        $sheet->setCellValue('K1', 'WTP की लोकेशन');
        $sheet->setCellValue('L1', 'WTP की क्षमता (MLD में)');
        $row = 2;
        foreach ($items as $index => $data) {
            $sheet->setCellValue('A' . $row, $index + 1);
            $sheet->setCellValue('B' . $row, $data->unique_id ?? 'N/A');
            $sheet->setCellValue('C' . $row, $data->ulb->district->name ?? 'N/A');
            $sheet->setCellValue('D' . $row, $data->ulb->name ?? 'N/A');
            $sheet->setCellValue('E' . $row, $data->financial_year ?? 'N/A');
            $sheet->setCellValue('F' . $row, $data->dateInput ?? 'N/A');
            $sheet->setCellValue('G' . $row, $data->yojna_name ?? 'N/A');
            $sheet->setCellValue('H' . $row, $data->phe_handover_date ?? 'N/A');
            $sheet->setCellValue('I' . $row, $data->not_handover_reason ?? 'N/A');
            $sheet->setCellValue('J' . $row, $data->wtp_number ?? 'N/A');
            $sheet->setCellValue('K' . $row, $data->wtp_location ?? 'N/A');
            $sheet->setCellValue('L' . $row, $data->wtp_capcity ?? 'N/A');

            $row++;
        }

        $writer = new Xlsx($spreadsheet);
        $filename = 'Peyjal_Records.xlsx';
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        $writer->save('php://output');
        exit;
    }
    public function exportExceljaljanitbimari()
    {
        $items = jaljanitbimari::get();
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        $sheet->setCellValue('A1', 'क़');
        $sheet->setCellValue('B1', 'यूनिक नंबर');
        $sheet->setCellValue('C1', 'जिला का नाम');
        $sheet->setCellValue('D1', 'निकाय का नाम ');
        $sheet->setCellValue('E1', 'वित्तीय वर्ष');
        $sheet->setCellValue('F1', 'दिनांक');
        $sheet->setCellValue('G1', 'कुल टंकिया');
        $sheet->setCellValue('H1', 'सफाई की गई टंकिया');
        $sheet->setCellValue('I1', 'सफाई नहीं की गई टंकिया');
        $sheet->setCellValue('J1', 'कुल सम्पवेल');
        $sheet->setCellValue('K1', 'सफाई की गई सम्पवेल');
        $sheet->setCellValue('L1', 'सफाई नहीं की गई सम्पवेल');
        $sheet->setCellValue('M1', 'कुल पावर पंप युक्त नलकूपों  की जानकारी');
        $sheet->setCellValue('N1', 'क्लोरिनेशन किया गया');
        $sheet->setCellValue('O1', 'क्लोरिनेशन नहीं किया गया');
        $sheet->setCellValue('P1', 'कुल जाचं');
        $sheet->setCellValue('Q1', 'पयेजल हेतु उपयुक्त पाया गया');
        $sheet->setCellValue('R1', 'पयेजल हेतु उपयुक्त नहीं पाया गया');
        $sheet->setCellValue('S1', 'माह मे सुधारे गये पाईप लाईन लीकेज की संख्या');
        $sheet->setCellValue('T1', 'माह के अंत मे लंबित लीकेज की संख्या');
        $row = 2;
        foreach ($items as $index => $data) {
            $sheet->setCellValue('A' . $row, $index + 1);
            $sheet->setCellValue('B' . $row, $data->unique_id ?? 'N/A');
            $sheet->setCellValue('C' . $row, $data->ulb->district->name ?? 'N/A');
            $sheet->setCellValue('D' . $row, $data->ulb->name ?? 'N/A');
            $sheet->setCellValue('E' . $row, $data->financial_year ?? 'N/A');
            $sheet->setCellValue('F' . $row, $data->dateInput ?? 'N/A');
            $sheet->setCellValue('G' . $row, $data->kultankiya ?? 'N/A');
            $sheet->setCellValue('H' . $row, $data->cleantanki ?? 'N/A');
            $sheet->setCellValue('I' . $row, $data->notcleantanki ?? 'N/A');
            $sheet->setCellValue('J' . $row, $data->totalsamvel ?? 'N/A');
            $sheet->setCellValue('K' . $row, $data->cleansamvel ?? 'N/A');
            $sheet->setCellValue('L' . $row, $data->notcleansamvel ?? 'N/A');
            $sheet->setCellValue('M' . $row, $data->totalpump ?? 'N/A');
            $sheet->setCellValue('N' . $row, $data->pumpnation ?? 'N/A');
            $sheet->setCellValue('O' . $row, $data->notpumpnation ?? 'N/A');
            $sheet->setCellValue('P' . $row, $data->totalcheck ?? 'N/A');
            $sheet->setCellValue('Q' . $row, $data->payjalupukt ?? 'N/A');
            $sheet->setCellValue('R' . $row, $data->notpayjalupukt ?? 'N/A');
            $sheet->setCellValue('S' . $row, $data->totalpipelinelikage ?? 'N/A');
            $sheet->setCellValue('T' . $row, $data->pipelinelikage ?? 'N/A');


            $row++;
        }

        $writer = new Xlsx($spreadsheet);
        $filename = 'Jaljanitbimari_Records.xlsx';
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        $writer->save('php://output');
        exit;
    }


    public function retirementlist()
    {
        $currentDate = Carbon::now();
        $nextMonthDate = Carbon::now()->addMonth();

        // Fetch only employees retiring this or next month
        $items = Employee::all()->filter(function ($employee) use ($currentDate, $nextMonthDate) {
            $retirementDate = Carbon::parse($employee->birth_date)->addYears(62)->endOfMonth();

            return $retirementDate->isSameMonth($currentDate) || $retirementDate->isSameMonth($nextMonthDate);
        })->sortBy(function ($employee) use ($currentDate) {
            $retirementDate = Carbon::parse($employee->birth_date)->addYears(62)->endOfMonth();
            return $retirementDate->isSameMonth($currentDate) ? 0 : 1;
        });

        return view('departments.retirementlist', compact('items'));
    }

    public function censusreport()
    {
        $items = SensReport::get();
        $nikays = User::all();
        return view('departments.censusreport', compact('items', 'nikays'));
    }

    //    public function electricitymaindata()
    // {

    //     return view('departments/electricitymaindata');
    // }


    public function electricitymaindata()
    {
        $verifieddata = ArrearsReport::count();
        $excluded = ArrearsReport::pluck('bp_number');

        $electricityseptdata = BakayaRashiTwo::whereNotIn('business_partner', $excluded)->count();
        $overalldata = BakayaRashi::whereNotIn('business_partner', $excluded)->count();
        $excludedother = BakayaRashiTwo::pluck('business_partner');
        $newconnection = BakayaRashi::whereNotIn('business_partner', $excludedother)->count();
        return view('departments/electricitymaindata', compact('verifieddata', 'electricityseptdata', 'overalldata', 'newconnection'));
    }

    public function verifieddata()
    {
        $items = ArrearsReport::all();


        return view('departments/verifieddata', compact('items'));
    }
    public function electricityseptdata()
    {
        // get all bp_number from arrears_reports
        $excluded = ArrearsReport::pluck('bp_number');

        // fetch only business_partner and total_balance from bakaya_rashis
        $items = BakayaRashiTwo::whereNotIn('business_partner', $excluded)
            ->select('business_partner', 'total_arrears')
            ->get();

        return view('departments.electricityseptdata', compact('items'));
    }

    public function overalldata()
    {
        $excluded = ArrearsReport::pluck('bp_number');

        // fetch only business_partner and total_balance from bakaya_rashis
        $items = BakayaRashi::whereNotIn('business_partner', $excluded)
            ->select('business_partner', 'total_balance')
            ->get();

        return view('departments/overalldata', compact('items'));
    }
    public function newconnection()
    {
        // get all business_partner values from bakaya_rashi_twos
        $excluded = BakayaRashiTwo::pluck('business_partner');

        // fetch records from bakaya_rashis where business_partner not in above list
        $items = BakayaRashi::whereNotIn('business_partner', $excluded)
            ->select('business_partner', 'total_balance')
            ->get();

        return view('departments.newconnection', compact('items'));
    }

    // public function ulbVerifiedCounts()
    // {
    //     // Get all ULBs first
    //     $allUlbs = User::all();

    //     // Get verification counts and total bakaya amount for ULBs that have verifications
    //     $verificationData = BpVerification::select(
    //         'bp_verifications.ulb_id',
    //         DB::raw('count(*) as total'),
    //         DB::raw('SUM(COALESCE(bakaya_rashis.total_balance, 0)) as total_amount')
    //     )
    //         ->leftJoin('bakaya_rashis', 'bp_verifications.bp_number', '=', 'bakaya_rashis.business_partner')
    //         ->where('bp_verifications.status', 'verified')
    //         ->groupBy('bp_verifications.ulb_id')
    //         ->get()
    //         ->keyBy('ulb_id');

    //     // Get counts of not verified BPs for each ULB
    //     $notVerifiedCounts = DB::table('bp_not_verified')
    //         ->select('ulb_id', DB::raw('count(*) as total_not_verified'))
    //         ->groupBy('ulb_id')
    //         ->pluck('total_not_verified', 'ulb_id')
    //         ->toArray();

    //     // Combine data for all ULBs, including those with 0 counts
    //     $datas = $allUlbs->map(function ($ulb) use ($verificationData, $notVerifiedCounts) {
    //         $ulbData = $verificationData->get($ulb->id);
    //         return (object)[
    //             'ulb' => $ulb,
    //             'total' => $ulbData ? $ulbData->total : 0,
    //             'totalAmount' => $ulbData ? $ulbData->total_amount : 0,
    //             'total_not_verified' => $notVerifiedCounts[$ulb->id] ?? 0,
    //             'bothCount' => ($ulbData ? $ulbData->total : 0) + ($notVerifiedCounts[$ulb->id] ?? 0)
    //         ];
    //     })->sortByDesc('total'); // Sort in descending order by total count

    //     return view('departments.ulbverifiedcounts', compact('datas'));
    // }
    public function ulbVerifiedCounts()
    {
        // 🔹 Get all ULBs first
        $allUlbs = User::select('id', 'name')->get()->keyBy('id');

        // 🔹 Raw SQL to get verified counts + bakaya + august totals per ULB
        $verifiedData = DB::select("
        SELECT 
            u.id AS ulb_id,
            COALESCE(v.total_verified, 0) AS total_verified,
            COALESCE(v.total_balance_bakaya, 0) AS total_bakaya_rashi,
            COALESCE(v.total_balance_august, 0) AS total_august_amount,
            COALESCE(v.total_balance_total, 0) AS total_balance_total
        FROM users u
        LEFT JOIN (
            SELECT 
                bv.ulb_id,
                COUNT(DISTINCT bv.bp_number) AS total_verified,
                SUM(COALESCE(br.total_balance, 0)) AS total_balance_bakaya,
                SUM(COALESCE(nb.total_balance, 0)) AS total_balance_august,
                SUM(COALESCE(br.total_balance, 0)) + SUM(COALESCE(nb.total_balance, 0)) AS total_balance_total
            FROM bp_verifications bv
            LEFT JOIN bakaya_rashis br ON br.business_partner = bv.bp_number
            LEFT JOIN nikay_bp_augusts nb ON nb.bp_number = bv.bp_number
            WHERE bv.status = 'verified'
            GROUP BY bv.ulb_id
        ) v ON v.ulb_id = u.id
        ORDER BY total_verified DESC
    ");

        // 🔹 Not verified counts
        $notVerifiedCounts = DB::select("
        SELECT ulb_id, COUNT(DISTINCT bp_number) AS total_not_verified
        FROM bp_not_verified
        GROUP BY ulb_id
    ");
        $notVerifiedMap = collect($notVerifiedCounts)->keyBy('ulb_id');

        // 🔹 Combine data for all ULBs
        $datas = collect($verifiedData)->map(function ($row) use ($allUlbs, $notVerifiedMap) {
            $ulb = $allUlbs->get($row->ulb_id);

            $notVerified = $notVerifiedMap->get($row->ulb_id);
            $totalNotVerified = $notVerified->total_not_verified ?? 0;

            return (object)[
                'ulb' => $ulb,
                'total_verified' => (int) $row->total_verified,
                'total_not_verified' => (int) $totalNotVerified,
                'bothCount' => (int) $row->total_verified + (int) $totalNotVerified,
                'total_bakaya_rashi' => (float) $row->total_bakaya_rashi,
                'total_august_amount' => (float) $row->total_august_amount,
                'total_balance_total' => (float) $row->total_balance_total,
            ];
        });

        // 🔹 Summary report
        $summaryReport = $this->generateSummaryReport();

        return view('departments.ulbverifiedcounts', compact('datas', 'summaryReport'));
    }



    private function generateSummaryReport()
    {
        // ✅ Use single queries per table — minimal repetition
        $bakaya = DB::table('bakaya_rashis')->selectRaw('COUNT(*) as total, SUM(total_balance) as amount')->first();
        $verified = BpVerification::leftJoin('bakaya_rashis', 'bp_verifications.bp_number', '=', 'bakaya_rashis.business_partner')
            ->selectRaw('COUNT(bp_verifications.id) as total, SUM(bakaya_rashis.total_balance) as amount')
            ->first();
        $notVerified = DB::table('bp_not_verified')
            ->leftJoin('bakaya_rashis', 'bp_not_verified.bp_number', '=', 'bakaya_rashis.business_partner')
            ->selectRaw('COUNT(bp_not_verified.id) as total, SUM(bakaya_rashis.total_balance) as amount')
            ->first();

        $verifiedCount = (int) ($verified->total ?? 0);
        $notVerifiedCount = (int) ($notVerified->total ?? 0);
        $verifiedAmount = (float) ($verified->amount ?? 0);
        $notVerifiedAmount = (float) ($notVerified->amount ?? 0);

        $restBPCount = max(0, ($bakaya->total ?? 0) - ($verifiedCount + $notVerifiedCount));
        $restBPAmount = max(0, ($bakaya->amount ?? 0) - ($verifiedAmount + $notVerifiedAmount));

        return (object) [
            'total_cscb_count' => (int) ($bakaya->total ?? 0),
            'total_cscb_amount' => (float) ($bakaya->amount ?? 0),
            'total_verified_count' => $verifiedCount,
            'total_verified_amount' => $verifiedAmount,
            'total_not_verified_count' => $notVerifiedCount,
            'total_not_verified_amount' => $notVerifiedAmount,
            'rest_bp_count' => $restBPCount,
            'rest_bp_amount' => $restBPAmount,
        ];
    }

    public function ulbVerifiedDetails($ulb_id)
    {
        // Get ULB information
        $ulb = User::findOrFail($ulb_id);

        // Get all verified BP numbers for this ULB with details from bakaya_rashis using left join
        $verifiedBpNumbers = BpVerification::leftJoin('bakaya_rashis', 'bp_verifications.bp_number', '=', 'bakaya_rashis.business_partner')
            ->where('bp_verifications.ulb_id', $ulb_id)
            ->where('bp_verifications.status', 'verified')
            ->select(
                'bp_verifications.*',
                'bakaya_rashis.business_partner',
                'bakaya_rashis.total_balance',
                'bakaya_rashis.division as bakaya_ulb_id'
            )
            ->orderBy('bp_verifications.verified_at', 'desc')
            ->get();

        // Format the data for the view
        $bpDetails = [];
        foreach ($verifiedBpNumbers as $verification) {
            $bakayaDetail = null;
            if ($verification->business_partner) {
                $bakayaDetail = (object) [
                    'business_partner' => $verification->business_partner,
                    'total_balance' => $verification->total_balance,
                    'ulb_id' => $verification->bakaya_ulb_id
                ];
            }

            $bpDetails[] = [
                'verification' => (object) [
                    'id' => $verification->id,
                    'bp_number' => $verification->bp_number,
                    'ulb_id' => $verification->ulb_id,
                    'status' => $verification->status,
                    'verified_at' => $verification->verified_at ? Carbon::parse($verification->verified_at) : null,
                ],
                'bakaya_detail' => $bakayaDetail
            ];
        }

        return view('departments.ulbverifieddetails', compact('ulb', 'bpDetails'));
    }


    public function allUlbVerifiedDetails()
    {
        // Get all verified BP numbers with details from bakaya_rashis using left join
        $verifiedBpNumbers = BpVerification::leftJoin('bakaya_rashis', 'bp_verifications.bp_number', '=', 'bakaya_rashis.business_partner')
            ->leftJoin('users', 'bp_verifications.ulb_id', '=', 'users.id')
            ->where('bp_verifications.status', 'verified')
            ->select(
                'bp_verifications.*',
                'bakaya_rashis.business_partner',
                'bakaya_rashis.total_balance',
                'bakaya_rashis.division as bakaya_ulb_id',
                'users.name as ulb_name'
            )
            ->orderBy('bp_verifications.verified_at', 'desc')
            ->get();

        // Format the data for the view
        $bpDetails = [];
        foreach ($verifiedBpNumbers as $verification) {
            $bakayaDetail = null;
            if ($verification->business_partner) {
                $bakayaDetail = (object) [
                    'business_partner' => $verification->business_partner,
                    'total_balance' => $verification->total_balance,
                    'ulb_id' => $verification->bakaya_ulb_id
                ];
            }

            $bpDetails[] = [
                'verification' => (object) [
                    'id' => $verification->id,
                    'bp_number' => $verification->bp_number,
                    'ulb_id' => $verification->ulb_id,
                    'ulb_name' => $verification->ulb_name,
                    'status' => $verification->status,
                    'verified_at' => $verification->verified_at ? Carbon::parse($verification->verified_at) : null,
                ],
                'bakaya_detail' => $bakayaDetail
            ];
        }
        // dd($bpDetails);
        return view('departments.allulbverifieddetails', compact('bpDetails'));
    }

    public function bpNotVerifiedDetails()
    {
        $datas = BpNotVerified::with('ulb')->get();
        return view('departments.bpnotverifiedlist', compact('datas'));
    }
    public function bpAllList()
    {
        $datas = BpVerification::with('ulb')
            ->leftJoin('bakaya_rashis', 'bp_verifications.bp_number', '=', 'bakaya_rashis.business_partner')
            ->leftJoin('nikay_bp_augusts', 'bp_verifications.bp_number', '=', 'nikay_bp_augusts.bp_number')
            ->select(
                'bp_verifications.*',
                'bakaya_rashis.total_balance as bakaya_total_balance',
                'nikay_bp_augusts.total_balance as nikay_total_balance'
            )
            ->paginate(100); // Add pagination with 100 items per page
        return view('departments.allbplist', compact('datas'));
    }

    // public function bpAllListAugust()
    // {
    //     $table = (new NikayBpAugust())->getTable();

    //     $datas = NikayBpAugust::leftJoin('bakaya_rashis', $table . '.bp_number', '=', 'bakaya_rashis.bp_number')
    //         ->leftJoin('bakaya_rashis', $table . '.bp_number', '=', 'bakaya_rashis.business_partner')
    //         ->select(
    //             $table . '.*',
    //             'bakaya_rashis.bp_number as verified_bp',
    //             'bakaya_rashis.amount as verified_amount',
    //             'bakaya_rashis.total_balance as bakaya_total_balance',
    //             DB::raw('COALESCE(bakaya_rashis.amount, bakaya_rashis.total_balance) as matched_amount')
    //         )
    //         ->paginate(100);

    //     return view('departments.allbplistaugust', compact('datas'));
    // }
    public function bpAllListAugust()
    {
        $table = (new NikayBpAugust())->getTable();

        $datas = NikayBpAugust::leftJoin('bakaya_rashis', $table . '.bp_number', '=', 'bakaya_rashis.business_partner')
            ->select(
                $table . '.*',
                'bakaya_rashis.total_balance as bakaya_total_balance'
            )
            ->paginate(100);

        return view('departments.allbplistaugust', compact('datas'));
    }


    public function exportExcelBpAllAugust()
    {
        $table = (new NikayBpAugust())->getTable();

        $items = NikayBpAugust::leftJoin('bakaya_rashis', $table . '.bp_number', '=', 'bakaya_rashis.business_partner')
            ->select(
                $table . '.*',
                'bakaya_rashis.total_balance as bakaya_total_balance'
            )
            ->get();

        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Set headers
        $sheet->setCellValue('A1', 'Sno.');
        $sheet->setCellValue('B1', 'BP Number');
        $sheet->setCellValue('C1', 'Consumer Name');
        $sheet->setCellValue('D1', 'Tariff Type');
        $sheet->setCellValue('E1', 'Address');
        $sheet->setCellValue('F1', 'Nikay BP August Total Balance');
        $sheet->setCellValue('G1', 'Bakaya Rashis Total Balance');

        // Fill data
        $row = 2;
        foreach ($items as $index => $data) {
            $sheet->setCellValue('A' . $row, $index + 1);
            $sheet->setCellValue('B' . $row, $data->bp_number ?? 'N/A');
            $sheet->setCellValue('C' . $row, $data->consumer_name ?? 'N/A');
            $sheet->setCellValue('D' . $row, $data->tariff_type ?? 'N/A');
            $sheet->setCellValue('E' . $row, $data->address ?? 'N/A');
            $sheet->setCellValue('F' . $row, $data->total_balance ?? '0');
            $sheet->setCellValue('G' . $row, $data->bakaya_total_balance ?? '0');
            $row++;
        }

        $writer = new Xlsx($spreadsheet);
        $filename = 'BP_All_List_August_report.xlsx';
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        $writer->save('php://output');
        exit;
    }

    public function exportExcelBpAll()
    {
        $items = BpVerification::with('ulb')
            ->leftJoin('bakaya_rashis', 'bp_verifications.bp_number', '=', 'bakaya_rashis.business_partner')
            ->leftJoin('nikay_bp_augusts', 'bp_verifications.bp_number', '=', 'nikay_bp_augusts.bp_number')
            ->select(
                'bp_verifications.*',
                'bakaya_rashis.total_balance as bakaya_total_balance',
                'nikay_bp_augusts.total_balance as nikay_total_balance'
            )
            ->get();

        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Set headers
        $sheet->setCellValue('A1', 'Sno.');
        $sheet->setCellValue('B1', 'BP Number');
        $sheet->setCellValue('C1', 'District');
        $sheet->setCellValue('D1', 'ULB Name');
        $sheet->setCellValue('E1', 'ULB Type');
        $sheet->setCellValue('F1', 'Status');
        $sheet->setCellValue('G1', 'Verified At');
        $sheet->setCellValue('H1', 'Bakaya Rashis Total Balance');
        $sheet->setCellValue('I1', 'Nikay BP August Total Balance');

        // Fill data
        $row = 2;
        foreach ($items as $index => $data) {
            $sheet->setCellValue('A' . $row, $index + 1);
            $sheet->setCellValue('B' . $row, $data->bp_number ?? 'N/A');
            $sheet->setCellValue('C' . $row, $data->ulb->district->name ?? 'N/A');
            $sheet->setCellValue('D' . $row, $data->ulb->name ?? 'N/A');
            $sheet->setCellValue('E' . $row, $data->ulb->ulb_type ?? 'N/A');
            $sheet->setCellValue('F' . $row, $data->status ?? 'N/A');
            $sheet->setCellValue('G' . $row, $data->verified_at ? Carbon::parse($data->verified_at)->format('Y-m-d H:i:s') : 'N/A');
            $sheet->setCellValue('H' . $row, $data->bakaya_total_balance ?? '0');
            $sheet->setCellValue('I' . $row, $data->nikay_total_balance ?? '0');
            $row++;
        }

        // Create Excel file
        $writer = new Xlsx($spreadsheet);
        $filename = 'BP_List_' . date('Y-m-d') . '.xlsx';

        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        $writer->save('php://output');
        exit;
    }

    public function updateFromExcel()
    {
        $filePath = storage_path('app/updates/sql_query.xlsx');

        $spreadsheet = IOFactory::load($filePath);
        $sheet = $spreadsheet->getActiveSheet();
        $highestRow = $sheet->getHighestRow();

        DB::beginTransaction();

        try {
            for ($row = 1; $row <= $highestRow; $row++) { // Start from row 2 (skip header)
                $bpNumber = $sheet->getCell('A' . $row)->getValue();
                $totalBalance = $sheet->getCell('B' . $row)->getValue();

                if ($bpNumber && $totalBalance !== null) {
                    DB::table('nikay_bp_augusts')
                        ->where('bp_number', $bpNumber)
                        ->update(['total_balance' => $totalBalance]);
                }
            }

            DB::commit();
            return "Updated successfully!";
        } catch (\Exception $e) {
            DB::rollBack();
            return "Error: " . $e->getMessage();
        }
    }
}
