@include('departments/header')

<div class="container-scroller">
    <!-- partial:partials/_navbar.html -->
    @include('departments/topbar')
    <!-- partial -->
    <div class="container-fluid page-body-wrapper">

        @include('departments/sidebar')

        <!-- partial -->
        <div class="main-panel">
            <div class="content-wrapper p-2">
                <div class="bg-primary-subtle d-flex justify-content-between align-middle p-2 rounded border">

                    <div>
                        <img src="{{ asset('images/cmsir.jpg') }}" width="60px" height="auto" />
                    </div>
                    <div class="dascen">
                        <h3><b>Verified Counts</b></h3>
                    </div>
                    <div>
                        <img src="{{ asset('images/newdeptycm.jpeg') }}" width="60px" height="auto" />
                    </div>
                </div>
                <div class="d-flex mt-3 mb-2 justify-content-between">
                    <div>
                        <button type="button" onclick="window.history.back()"
                            class="btn btn-dark rounded btn-sm">Back</button>
                    </div>
                </div>

                <!-- Summary Report Table -->
                <div class="mt-3 mb-4">
                    <h5 class="mb-3"><b>Summary Report</b></h5>
                    <div class="table-responsive">
                        <table class="table table-bordered align-items-center">
                            <thead class="table-primary">
                                <tr>
                                    <th>Total Data From CSCB</th>
                                    <th>Total Data From CSCB Amount</th>
                                    <th>Total Verified Data Count</th>
                                    <th>Total Verified Data Amount</th>
                                    <th>Total Not Verified Data Count</th>
                                    <th>Total Not Verified Data Amount</th>
                                    <th>Rest BP Number Count</th>
                                    <th>Rest BP Number Amount</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr class="fw-bold">
                                    <td>{{ $summaryReport->total_cscb_count }}</td>
                                    <td>{{ number_format($summaryReport->total_cscb_amount, 2) }}</td>
                                    <td>{{ $summaryReport->total_verified_count }}</td>
                                    <td>{{ number_format($summaryReport->total_verified_amount, 2) }}</td>
                                    <td>{{ $summaryReport->total_not_verified_count }}</td>
                                    <td>{{ number_format($summaryReport->total_not_verified_amount, 2) }}</td>
                                    <td>{{ $summaryReport->rest_bp_count }}</td>
                                    <td>{{ number_format($summaryReport->rest_bp_amount, 2) }}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- ULB-wise Data Table -->
                <div class="mt-3 desktopset">
                    <h5 class="mb-3"><b>ULB-wise Report</b></h5>
                    <div class="mb-3">
                        <table class="table table-bordered align-items-center">
                            <thead>
                                <tr>
                                    <th>ULB Name</th>
                                    <th>Total Verified</th>
                                    <th>Total Not Verified</th>
                                    <th>Total (Both)</th>
                                    <th>Total Bakaya Rashi (Verified BPs)</th>
                                    <th>Total August Payment</th>
                                    <th>Total Combined (Bakaya + August)</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($datas as $data)
                                    <tr>
                                        <td>{{ $data->ulb->name ?? 'N/A' }}</td>
                                        <td>{{ $data->total_verified }}</td>
                                        <td>{{ $data->total_not_verified }}</td>
                                        <td>{{ $data->bothCount }}</td>
                                        <td>{{ number_format($data->total_bakaya_rashi, 2) }}</td>
                                        <td>{{ number_format($data->total_august_amount, 2) }}</td>
                                        <td>{{ number_format($data->total_balance_total, 2) }}</td>
                                        <td>
                                            <a href="{{ route('ulb.verified.details', $data->ulb->id) }}"
                                                class="btn btn-sm btn-primary">
                                                View
                                            </a>
                                        </td>
                                    </tr>
                                @endforeach

                                {{-- 🔹 Grand Totals Row --}}
                                <tr class="fw-bold bg-light">
                                    <td>Total</td>
                                    <td>{{ $datas->sum('total_verified') }}</td>
                                    <td>{{ $datas->sum('total_not_verified') }}</td>
                                    <td>{{ $datas->sum('bothCount') }}</td>
                                    <td>{{ number_format($datas->sum('total_bakaya_rashi'), 2) }}</td>
                                    <td>{{ number_format($datas->sum('total_august_amount'), 2) }}</td>
                                    <td>{{ number_format($datas->sum('total_balance_total'), 2) }}</td>
                                    <td></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <footer class="footer">
                    <div class="d-sm-flex justify-content-center justify-content-sm-between">
                        <span class="text-muted text-center text-sm-left d-block d-sm-inline-block">Copyright © 2024.
                            href="#" target="_blank">Designed by Datacenter</a>. All rights reserved.</span>
                    </div>
                </footer>
            </div>
        </div>
    </div>


    @include('departments/footer')
