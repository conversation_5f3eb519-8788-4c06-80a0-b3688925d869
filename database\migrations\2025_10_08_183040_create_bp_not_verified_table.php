<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('bp_not_verified', function (Blueprint $table) {
            $table->id();
            $table->string('bp_number');
            $table->unsignedBigInteger('ulb_id');
            $table->text('address');
            $table->date('date_of_submission');
            $table->timestamps();

            // Foreign key constraint
            $table->foreign('ulb_id')->references('id')->on('users')->onDelete('cascade');

            // Index for better performance
            $table->index(['ulb_id', 'bp_number']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('bp_not_verified');
    }
};
