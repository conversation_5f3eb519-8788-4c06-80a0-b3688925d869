@include('ulbdashboard/header')

<div class="container-scroller">
    <!-- partial:partials/_navbar.html -->
    @include('ulbdashboard/topnav')
    <!-- partial -->
    <div class="container-fluid page-body-wrapper">

        @include('ulbdashboard/sidenav')

        <!-- partial -->
        <div class="main-panel">
            <div class="content-wrapper p-2">
                <div class="bg-primary-subtle d-flex justify-content-between align-middle p-2 rounded border">

                    <div>
                        <img src="{{ asset('images/cmsir.jpg') }}" width="60px" height="auto" />
                    </div>
                    <div class="dascen">
                        <h3><b>BP Not Verified Form</b></h3>
                    </div>
                    <div>
                        <img src="{{ asset('images/newdeptycm.jpeg') }}" width="60px" height="auto" />
                    </div>
                </div>

                <div class="d-flex mt-3 mb-2 justify-content-between">
                    <div>
                        <button type="button" onclick="window.history.back()"
                            class="btn btn-dark rounded btn-sm">Back</button>
                    </div>
                    <div>
                        <a href="{{ route('bp.not.verified.list') }}" class="btn btn-info btn-sm">
                            <i class="fas fa-list"></i> View Submitted Records
                        </a>
                    </div>
                </div>

                <!-- Form Section -->
                <div class="row mt-3">
                    <div class="col-md-8 mx-auto">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0">Submit BP Not Verified Details</h5>
                            </div>
                            <div class="card-body">
                                <form method="POST" action="{{ route('bp.not.verified.submit') }}">
                                    @csrf
                                    
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="bp_number" class="form-label">BP Number <span class="text-danger">*</span></label>
                                                <input type="text" class="form-control @error('bp_number') is-invalid @enderror" 
                                                       id="bp_number" name="bp_number" value="{{ old('bp_number') }}" 
                                                       placeholder="Enter BP Number" required>
                                                @error('bp_number')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                        
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="date_of_submission" class="form-label">Date of Submission <span class="text-danger">*</span></label>
                                                <input type="date" class="form-control @error('date_of_submission') is-invalid @enderror" 
                                                       id="date_of_submission" name="date_of_submission" 
                                                       value="{{ old('date_of_submission', date('Y-m-d')) }}" required>
                                                @error('date_of_submission')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="address" class="form-label">Address <span class="text-danger">*</span></label>
                                        <textarea class="form-control @error('address') is-invalid @enderror" 
                                                  id="address" name="address" rows="4" 
                                                  placeholder="Enter complete address" required>{{ old('address') }}</textarea>
                                        @error('address')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                        <button type="reset" class="btn btn-secondary me-md-2">Reset</button>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save"></i> Submit
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Submissions -->
                @if($submittedRecords->count() > 0)
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header bg-info text-white">
                                <h5 class="mb-0">Recent Submissions (Last 5)</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-bordered table-hover">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>S.No</th>
                                                <th>BP Number</th>
                                                <th>Address</th>
                                                <th>Date of Submission</th>
                                                <th>Submitted On</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($submittedRecords->take(5) as $index => $record)
                                            <tr>
                                                <td>{{ $index + 1 }}</td>
                                                <td><strong>{{ $record->bp_number }}</strong></td>
                                                <td>{{ Str::limit($record->address, 50) }}</td>
                                                <td>{{ $record->date_of_submission->format('d-m-Y') }}</td>
                                                <td>{{ $record->created_at->format('d-m-Y H:i') }}</td>
                                            </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                                @if($submittedRecords->count() > 5)
                                <div class="text-center mt-3">
                                    <a href="{{ route('bp.not.verified.list') }}" class="btn btn-outline-primary">
                                        View All {{ $submittedRecords->count() }} Records
                                    </a>
                                </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
                @endif

                <footer class="footer mt-4">
                    <div class="d-sm-flex justify-content-center justify-content-sm-between">
                        <span class="text-muted text-center text-sm-left d-block d-sm-inline-block">Copyright © 2024. <a
                                href="#" target="_blank">Designed by Datacenter</a>. All rights reserved.</span>
                    </div>
                </footer>
            </div>
        </div>
    </div>
</div>

@include('ulbdashboard/footer')
